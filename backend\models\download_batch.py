#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载批次模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, ForeignKey, JSON, BigInteger
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base
from datetime import datetime
from typing import Dict, Any, Optional
import uuid

class DownloadBatch(Base):
    """下载批次模型 - 记录每次下载操作的批次信息"""
    
    __tablename__ = 'download_batches'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    batch_id = Column(String(50), unique=True, nullable=False, comment='批次唯一标识')
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True, comment='用户ID')
    session_id = Column(String(100), nullable=True, comment='会话ID')
    
    # 批次信息
    batch_type = Column(String(20), nullable=False, comment='批次类型: single/batch/folder')
    target_type = Column(String(20), nullable=False, comment='目标类型: file/folder')
    target_id = Column(Integer, nullable=True, comment='目标ID（文件或文件夹）')
    
    # 统计信息
    total_files = Column(Integer, default=0, comment='文件总数')
    total_size = Column(BigInteger, default=0, comment='总大小（字节）')
    compressed_size = Column(BigInteger, default=0, comment='压缩后大小（字节）')
    
    # 下载信息
    download_name = Column(String(255), nullable=False, comment='下载包名称')
    is_encrypted = Column(Boolean, default=False, comment='是否加密')
    password_required = Column(Boolean, default=False, comment='是否需要密码')
    
    # 请求信息
    ip_address = Column(String(45), nullable=True, comment='请求IP地址')
    user_agent = Column(Text, nullable=True, comment='用户代理')
    referer = Column(Text, nullable=True, comment='来源页面')
    download_source = Column(String(50), default='web', comment='下载来源: web/api/mobile')
    
    # 状态信息
    status = Column(String(20), default='preparing', comment='状态: preparing/ready/downloading/completed/failed/expired')
    error_message = Column(Text, nullable=True, comment='错误信息')
    
    # 时间信息
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    started_at = Column(DateTime, nullable=True, comment='开始下载时间')
    completed_at = Column(DateTime, nullable=True, comment='完成时间')
    expires_at = Column(DateTime, nullable=True, comment='过期时间')
    
    # 扩展信息
    extra_data = Column(JSON, nullable=True, comment='扩展元数据')
    
    # 关联关系
    user = relationship("User", backref="download_batches")
    download_records = relationship("DownloadRecord", backref="batch", cascade="all, delete-orphan")

    def __init__(self, batch_type: str, target_type: str, download_name: str, 
                 target_id: int = None, user_id: int = None, **kwargs):
        self.batch_id = str(uuid.uuid4())
        self.batch_type = batch_type
        self.target_type = target_type
        self.download_name = download_name
        self.target_id = target_id
        self.user_id = user_id
        
        # 设置其他属性
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def start_download(self):
        """开始下载"""
        self.status = 'downloading'
        self.started_at = datetime.now()
    
    def complete_download(self):
        """完成下载"""
        self.status = 'completed'
        self.completed_at = datetime.now()
    
    def fail_download(self, error_message: str):
        """下载失败"""
        self.status = 'failed'
        self.error_message = error_message
        self.completed_at = datetime.now()
    
    def mark_ready(self):
        """标记为准备就绪"""
        self.status = 'ready'
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if not self.expires_at:
            return False
        return datetime.now() > self.expires_at
    
    def get_download_duration(self) -> Optional[float]:
        """获取下载持续时间（秒）"""
        if not self.started_at or not self.completed_at:
            return None
        return (self.completed_at - self.started_at).total_seconds()
    
    def get_compression_ratio(self) -> Optional[float]:
        """获取压缩比"""
        if not self.total_size or not self.compressed_size:
            return None
        return self.compressed_size / self.total_size
    
    def add_extra_data(self, key: str, value: Any):
        """添加扩展数据"""
        if not self.extra_data:
            self.extra_data = {}
        self.extra_data[key] = value
    
    def get_extra_data(self, key: str, default: Any = None) -> Any:
        """获取扩展数据"""
        if not self.extra_data:
            return default
        return self.extra_data.get(key, default)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'batch_id': self.batch_id,
            'user_id': self.user_id,
            'session_id': self.session_id,
            'batch_type': self.batch_type,
            'target_type': self.target_type,
            'target_id': self.target_id,
            'total_files': self.total_files,
            'total_size': self.total_size,
            'compressed_size': self.compressed_size,
            'download_name': self.download_name,
            'is_encrypted': self.is_encrypted,
            'password_required': self.password_required,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'download_source': self.download_source,
            'status': self.status,
            'error_message': self.error_message,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'download_duration': self.get_download_duration(),
            'compression_ratio': self.get_compression_ratio(),
            'is_expired': self.is_expired(),
            'extra_data': self.extra_data,
            'record_count': len(self.download_records) if hasattr(self, 'download_records') else 0
        } 