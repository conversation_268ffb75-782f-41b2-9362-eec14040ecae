@echo off
chcp 65001 >nul
echo 🔥 配置Windows防火墙以允许局域网访问
echo ================================================

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 已获得管理员权限
) else (
    echo ❌ 需要管理员权限才能配置防火墙
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo.
echo 🔧 正在配置防火墙规则...

:: 删除可能存在的旧规则
netsh advfirewall firewall delete rule name="文件共享系统-前端" >nul 2>&1
netsh advfirewall firewall delete rule name="文件共享系统-API" >nul 2>&1

:: 添加前端服务器端口规则 (8084)
echo 📝 添加前端服务器端口规则 (8084)...
netsh advfirewall firewall add rule name="文件共享系统-前端" dir=in action=allow protocol=TCP localport=8084
if %errorLevel% == 0 (
    echo ✅ 前端端口 8084 规则添加成功
) else (
    echo ❌ 前端端口 8084 规则添加失败
)

:: 添加API服务器端口规则 (8086)
echo 📝 添加API服务器端口规则 (8086)...
netsh advfirewall firewall add rule name="文件共享系统-API" dir=in action=allow protocol=TCP localport=8086
if %errorLevel% == 0 (
    echo ✅ API端口 8086 规则添加成功
) else (
    echo ❌ API端口 8086 规则添加失败
)

echo.
echo 🔍 当前防火墙规则:
netsh advfirewall firewall show rule name="文件共享系统-前端"
echo.
netsh advfirewall firewall show rule name="文件共享系统-API"

echo.
echo ================================================
echo ✅ 防火墙配置完成！
echo.
echo 📋 已配置的端口:
echo    前端服务器: 8084 (TCP)
echo    API服务器:  8086 (TCP)
echo.
echo 🌐 现在局域网内的其他设备应该可以访问您的文件共享系统了
echo.
echo 💡 如果仍然无法访问，请检查:
echo    1. 路由器是否阻止了内网通信
echo    2. 杀毒软件是否阻止了网络访问
echo    3. 网络是否在同一个局域网段
echo.
pause
