#!/usr/bin/env python3
"""
测试认证和下载记录功能
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:8086"

def test_login():
    """测试登录并获取token"""
    print("🔐 测试用户登录...")
    
    login_data = {
        "username": "fjj",
        "password": "123456"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        print(f"登录响应状态码: {response.status_code}")
        print(f"登录响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"登录响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('success'):
                token = data.get('token')
                user_info = data.get('user', {})
                print(f"✅ 登录成功: {user_info.get('username')} (ID: {user_info.get('id')})")
                print(f"Token: {token[:20]}..." if token else "No token")
                return token, user_info.get('id')
            else:
                print(f"❌ 登录失败: {data.get('message')}")
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"❌ 登录异常: {e}")
    
    return None, None

def test_token_verification(token):
    """测试token验证"""
    print(f"\n🔍 测试token验证...")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{BASE_URL}/api/auth/verify", headers=headers)
        print(f"验证响应状态码: {response.status_code}")
        print(f"验证响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"验证响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            if data.get('valid'):
                print("✅ Token验证成功")
                return True
            else:
                print(f"❌ Token验证失败: {data.get('error')}")
        else:
            print(f"❌ Token验证请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"❌ Token验证异常: {e}")
    
    return False

def test_download_records(token):
    """测试获取下载记录"""
    print(f"\n📋 测试获取下载记录...")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{BASE_URL}/api/download/records", headers=headers)
        print(f"下载记录响应状态码: {response.status_code}")
        print(f"下载记录响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"下载记录响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            print("✅ 获取下载记录成功")
            return True
        else:
            print(f"❌ 获取下载记录失败: {response.status_code}")
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"❌ 获取下载记录异常: {e}")
    
    return False

def test_without_token():
    """测试不带token的请求"""
    print(f"\n🚫 测试不带token的请求...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/download/records")
        print(f"无token响应状态码: {response.status_code}")
        print(f"无token响应头: {dict(response.headers)}")
        print(f"无token响应内容: {response.text}")
    except Exception as e:
        print(f"❌ 无token请求异常: {e}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 认证和下载记录功能测试")
    print("=" * 60)
    
    # 测试不带token的请求
    test_without_token()
    
    # 测试登录
    token, user_id = test_login()
    
    if token:
        # 测试token验证
        if test_token_verification(token):
            # 测试获取下载记录
            test_download_records(token)
        else:
            print("❌ Token验证失败，跳过下载记录测试")
    else:
        print("❌ 登录失败，无法进行后续测试")
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
