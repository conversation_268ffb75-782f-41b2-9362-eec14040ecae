#!/usr/bin/env python3
"""
获取局域网访问地址工具
"""

import socket
import subprocess
import platform

def get_local_ip():
    """获取本机局域网IP地址"""
    try:
        # 方法1: 通过socket连接获取
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        try:
            # 连接到一个不存在的地址，只是为了获取本机IP
            s.connect(('**************', 1))
            ip = s.getsockname()[0]
        except Exception:
            ip = '127.0.0.1'
        finally:
            s.close()
        return ip
    except Exception:
        return '127.0.0.1'

def get_all_network_interfaces():
    """获取所有网络接口的IP地址"""
    interfaces = []
    
    try:
        if platform.system() == "Windows":
            # Windows系统
            result = subprocess.run(['ipconfig'], capture_output=True, text=True, encoding='gbk')
            lines = result.stdout.split('\n')
            
            current_adapter = ""
            for line in lines:
                line = line.strip()
                if "适配器" in line or "adapter" in line.lower():
                    current_adapter = line
                elif "IPv4" in line and ":" in line:
                    ip = line.split(':')[-1].strip()
                    if ip and not ip.startswith('127.') and not ip.startswith('169.254.'):
                        interfaces.append({
                            'adapter': current_adapter,
                            'ip': ip
                        })
        else:
            # Linux/Mac系统
            result = subprocess.run(['hostname', '-I'], capture_output=True, text=True)
            if result.returncode == 0:
                ips = result.stdout.strip().split()
                for ip in ips:
                    if not ip.startswith('127.') and not ip.startswith('169.254.'):
                        interfaces.append({
                            'adapter': 'Network Interface',
                            'ip': ip
                        })
    except Exception as e:
        print(f"获取网络接口失败: {e}")
    
    return interfaces

def main():
    """主函数"""
    print("=" * 60)
    print("🌐 局域网访问地址获取工具")
    print("=" * 60)
    
    # 获取主要IP地址
    main_ip = get_local_ip()
    print(f"📍 主要IP地址: {main_ip}")
    
    # 获取所有网络接口
    interfaces = get_all_network_interfaces()
    
    if interfaces:
        print("\n🔍 所有可用的网络接口:")
        for i, interface in enumerate(interfaces, 1):
            print(f"  {i}. {interface['adapter']}: {interface['ip']}")
    
    print("\n🌐 局域网访问地址:")
    frontend_port = 8084
    api_port = 8086
    
    # 显示主要访问地址
    if main_ip != '127.0.0.1':
        print(f"  前端页面: http://{main_ip}:{frontend_port}")
        print(f"  登录页面: http://{main_ip}:{frontend_port}/login.html")
        print(f"  API地址:  http://{main_ip}:{api_port}")
    
    # 显示所有可用地址
    if interfaces:
        print("\n📋 所有可用访问地址:")
        for interface in interfaces:
            ip = interface['ip']
            print(f"  http://{ip}:{frontend_port} (前端)")
            print(f"  http://{ip}:{api_port} (API)")
            print()
    
    print("💡 使用说明:")
    print("  1. 确保防火墙允许相应端口访问")
    print("  2. 局域网内其他设备可使用上述IP地址访问")
    print("  3. 如果无法访问，请检查网络设置和防火墙配置")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
