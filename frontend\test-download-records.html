<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载记录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        pre {
            background: #f1f1f1;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .record-item {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 4px;
            background: #fff;
        }
        .record-header {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .record-meta {
            font-size: 0.9em;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>下载记录功能测试</h1>
        
        <div class="test-controls">
            <button class="btn" onclick="testLogin()">1. 测试登录</button>
            <button class="btn" onclick="testDownloadRecords()">2. 获取下载记录</button>
            <button class="btn" onclick="testDownloadFolder()">3. 下载文件夹</button>
            <button class="btn" onclick="clearResults()">清空结果</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8086/api';
        let authToken = null;

        function addResult(title, content, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `
                <h3>${title}</h3>
                <pre>${typeof content === 'object' ? JSON.stringify(content, null, 2) : content}</pre>
            `;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testLogin() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'fjj',
                        password: '123456'
                    })
                });

                const data = await response.json();
                
                if (data.success && data.token) {
                    authToken = data.token;
                    addResult('登录测试', `登录成功！用户: ${data.user.username} (ID: ${data.user.id})`, 'success');
                } else {
                    addResult('登录测试', `登录失败: ${data.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                addResult('登录测试', `请求失败: ${error.message}`, 'error');
            }
        }

        async function testDownloadRecords() {
            if (!authToken) {
                addResult('下载记录测试', '请先登录！', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/download/records`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    addResult('下载记录测试', {
                        message: `成功获取 ${data.records.length} 条记录`,
                        total: data.total,
                        records: data.records.slice(0, 3) // 只显示前3条
                    }, 'success');

                    // 显示记录详情
                    if (data.records.length > 0) {
                        const recordsDiv = document.createElement('div');
                        recordsDiv.innerHTML = '<h3>下载记录详情:</h3>';
                        
                        data.records.slice(0, 5).forEach((record, index) => {
                            const recordDiv = document.createElement('div');
                            recordDiv.className = 'record-item';
                            recordDiv.innerHTML = `
                                <div class="record-header">${record.filename}</div>
                                <div class="record-meta">
                                    <div>下载时间: ${record.download_time}</div>
                                    <div>文件大小: ${(record.file_size / 1024 / 1024).toFixed(2)} MB</div>
                                    <div>下载类型: ${record.download_type}</div>
                                    <div>IP地址: ${record.ip_address}</div>
                                    <div>是否加密: ${record.is_encrypted ? '是' : '否'}</div>
                                </div>
                            `;
                            recordsDiv.appendChild(recordDiv);
                        });
                        
                        document.getElementById('results').appendChild(recordsDiv);
                    }
                } else {
                    addResult('下载记录测试', `获取失败: ${data.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                addResult('下载记录测试', `请求失败: ${error.message}`, 'error');
            }
        }

        async function testDownloadFolder() {
            if (!authToken) {
                addResult('文件夹下载测试', '请先登录！', 'error');
                return;
            }

            try {
                addResult('文件夹下载测试', '开始下载文件夹...', 'info');
                
                const response = await fetch(`${API_BASE}/download/folder/4`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const blob = await response.blob();
                    addResult('文件夹下载测试', `下载成功！文件大小: ${(blob.size / 1024 / 1024).toFixed(2)} MB`, 'success');
                    
                    // 触发下载
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `folder_download_${Date.now()}.zip`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                } else {
                    const errorData = await response.json();
                    addResult('文件夹下载测试', `下载失败: ${errorData.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                addResult('文件夹下载测试', `请求失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
