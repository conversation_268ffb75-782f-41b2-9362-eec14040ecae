#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试依赖检查功能
"""

import subprocess
import sys
import os

def test_dependency_checker():
    """测试依赖检查器"""
    print("🧪 测试依赖检查功能...")
    print("=" * 50)
    
    # 检查依赖检查脚本是否存在
    if not os.path.exists("check_dependencies.py"):
        print("❌ 错误: check_dependencies.py 文件不存在")
        return False
    
    # 检查 requirements.txt 是否存在
    if not os.path.exists("backend/requirements.txt"):
        print("❌ 错误: backend/requirements.txt 文件不存在")
        return False
    
    try:
        # 运行依赖检查
        result = subprocess.run([sys.executable, "check_dependencies.py"], 
                              capture_output=True, text=True, encoding='utf-8')
        
        print("📋 依赖检查输出:")
        print("-" * 30)
        print(result.stdout)
        
        if result.stderr:
            print("⚠️  错误输出:")
            print(result.stderr)
        
        print(f"🔍 退出代码: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ 依赖检查通过")
        else:
            print("⚠️  检测到依赖问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_batch_file_syntax():
    """测试批处理文件语法"""
    print("\n🧪 测试批处理文件语法...")
    print("=" * 50)
    
    batch_file = "一键启动_SQLite.bat"
    
    if not os.path.exists(batch_file):
        print(f"❌ 错误: {batch_file} 文件不存在")
        return False
    
    try:
        # 读取批处理文件内容
        with open(batch_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 基本语法检查
        issues = []
        
        # 检查是否有未闭合的括号
        open_parens = content.count('(')
        close_parens = content.count(')')
        if open_parens != close_parens:
            issues.append(f"括号不匹配: 开括号 {open_parens} 个, 闭括号 {close_parens} 个")
        
        # 检查是否有基本的结构
        if "@echo off" not in content:
            issues.append("缺少 @echo off 命令")
        
        if "python --version" not in content:
            issues.append("缺少 Python 版本检查")
        
        if "check_dependencies.py" not in content:
            issues.append("缺少依赖检查调用")
        
        if issues:
            print("⚠️  发现的问题:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print("✅ 批处理文件语法检查通过")
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 文件共享系统 - 依赖检查测试工具")
    print("=" * 60)
    
    success = True
    
    # 测试依赖检查器
    if not test_dependency_checker():
        success = False
    
    # 测试批处理文件
    if not test_batch_file_syntax():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！")
        print("\n💡 建议:")
        print("1. 运行 一键启动_SQLite.bat 测试完整流程")
        print("2. 检查依赖安装是否正常工作")
        print("3. 验证国内镜像源是否加速下载")
    else:
        print("❌ 部分测试失败，请检查相关问题")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
