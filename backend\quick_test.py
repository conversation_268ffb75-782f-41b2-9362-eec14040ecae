#!/usr/bin/env python3
"""
快速测试下载记录功能
"""

import requests
import json

# API基础URL
BASE_URL = "http://localhost:8086"

def test_login_and_download():
    """测试登录并下载"""
    print("🔐 登录...")
    
    login_data = {
        "username": "fjj",
        "password": "123456"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                token = data.get('token')
                user_info = data.get('user', {})
                print(f"✅ 登录成功: {user_info.get('username')} (ID: {user_info.get('id')})")
                
                # 测试文件夹下载
                print("\n⬇️ 测试文件夹下载...")
                headers = {"Authorization": f"Bearer {token}"}
                
                download_response = requests.get(f"{BASE_URL}/api/download/folder/4", headers=headers)
                print(f"下载响应状态码: {download_response.status_code}")
                
                if download_response.status_code == 200:
                    print(f"✅ 下载成功，文件大小: {len(download_response.content)} 字节")
                else:
                    try:
                        error_data = download_response.json()
                        print(f"❌ 下载失败: {error_data}")
                    except:
                        print(f"❌ 下载失败: {download_response.text}")
                
                # 测试获取下载记录
                print("\n📊 测试获取下载记录...")
                records_response = requests.get(f"{BASE_URL}/api/download/records", headers=headers)
                print(f"记录响应状态码: {records_response.status_code}")
                
                if records_response.status_code == 200:
                    records_data = records_response.json()
                    print(f"✅ 获取记录成功: {records_data}")
                else:
                    try:
                        error_data = records_response.json()
                        print(f"❌ 获取记录失败: {error_data}")
                    except:
                        print(f"❌ 获取记录失败: {records_response.text}")
                
            else:
                print(f"❌ 登录失败: {data.get('message')}")
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 登录异常: {e}")

if __name__ == "__main__":
    test_login_and_download()
