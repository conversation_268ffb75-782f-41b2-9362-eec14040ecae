#!/usr/bin/env python3
"""
测试密码验证
"""

from config.database import DatabaseManager
from models.user import User

def test_password():
    """测试密码验证"""
    try:
        db_manager = DatabaseManager()
        db_manager.initialize()
        
        with db_manager.get_session() as session:
            user = session.query(User).filter_by(username='fjj').first()
            
            if user:
                print(f"找到用户: {user.username}")
                print(f"用户ID: {user.id}")
                print(f"密码哈希: {user.password_hash}")
                print(f"盐值: {user.salt}")
                
                # 测试密码
                test_passwords = ['123456', '123', 'fjj', 'admin']
                
                for pwd in test_passwords:
                    result = user.check_password(pwd)
                    print(f"密码 '{pwd}': {'✅ 正确' if result else '❌ 错误'}")
                    
            else:
                print("未找到用户 fjj")
                
    except Exception as e:
        print(f"测试密码失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_password()
