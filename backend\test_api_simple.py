#!/usr/bin/env python3
"""
简单的API测试
"""

import requests
import json

def test_login():
    """测试登录"""
    url = "http://localhost:8086/api/auth/login"
    
    data = {
        "username": "fjj",
        "password": "123456"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"发送请求到: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
        print(f"请求头: {headers}")
        
        response = requests.post(url, json=data, headers=headers)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                token = result.get('token')
                print(f"登录成功，token: {token[:20]}...")
                
                # 测试下载记录
                test_download_records(token)
            else:
                print(f"登录失败: {result}")
        else:
            print(f"HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"请求异常: {e}")

def test_download_records(token):
    """测试获取下载记录"""
    url = "http://localhost:8086/api/download/records"
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        print(f"\n发送下载记录请求到: {url}")
        print(f"请求头: {headers}")
        
        response = requests.get(url, headers=headers)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    test_login()
