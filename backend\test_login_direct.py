#!/usr/bin/env python3
"""
直接测试登录功能
"""

from config.database import DatabaseManager
from services.user_service import UserService

def test_login_direct():
    """直接测试登录功能"""
    try:
        db_manager = DatabaseManager()
        db_manager.initialize()
        
        user_service = UserService(db_manager)
        
        # 测试登录
        result = user_service.authenticate_user(
            username='fjj',
            password='123456',
            ip_address='127.0.0.1',
            user_agent='test'
        )
        
        print(f"登录结果: {result}")
        
        if result.get('success'):
            token = result.get('session_token')
            print(f"登录成功，token: {token}")
            
            # 测试token验证
            user_info = user_service.validate_session(token)
            print(f"Token验证结果: {user_info}")
            
        else:
            print(f"登录失败: {result.get('error')}")
                
    except Exception as e:
        print(f"测试登录失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_login_direct()
