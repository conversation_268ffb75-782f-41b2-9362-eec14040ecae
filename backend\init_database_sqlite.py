#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQLite数据库初始化脚本
"""

import sys
import os
import sqlite3
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_sample_data(cursor, connection):
    """创建示例数据用于测试收藏功能"""
    try:
        # 检查是否已有共享文件夹
        cursor.execute("SELECT COUNT(*) FROM shared_folders")
        folder_count = cursor.fetchone()[0]

        if folder_count == 0:
            print("创建示例共享文件夹...")
            # 创建示例文件夹
            sample_folders = [
                ("示例图片", "C:/shared/images", "包含示例图片文件的文件夹"),
                ("设计文件", "C:/shared/design", "包含设计文件的文件夹"),
                ("照片集", "C:/shared/photos", "包含照片的文件夹")
            ]

            folder_ids = []
            for name, path, desc in sample_folders:
                cursor.execute("""
                    INSERT INTO shared_folders (name, path, description, is_active, created_at)
                    VALUES (?, ?, ?, 1, ?)
                """, (name, path, desc, datetime.now().isoformat()))
                folder_ids.append(cursor.lastrowid)

            print(f"创建了 {len(sample_folders)} 个示例文件夹")
        else:
            print("共享文件夹已存在，跳过创建")
            # 获取现有文件夹ID
            cursor.execute("SELECT id FROM shared_folders LIMIT 3")
            folder_ids = [row[0] for row in cursor.fetchall()]

        # 检查是否已有共享文件
        cursor.execute("SELECT COUNT(*) FROM shared_files")
        file_count = cursor.fetchone()[0]

        if file_count == 0 and folder_ids:
            print("创建示例文件记录...")
            # 创建示例文件记录
            sample_files = [
                ("sample1.jpg", "images/sample1.jpg", 1024000, "image/jpeg", ".jpg", 1, folder_ids[0] if len(folder_ids) > 0 else 1),
                ("design.psd", "design/design.psd", 5120000, "image/vnd.adobe.photoshop", ".psd", 1, folder_ids[1] if len(folder_ids) > 1 else 1),
                ("photo1.png", "photos/photo1.png", 2048000, "image/png", ".png", 1, folder_ids[2] if len(folder_ids) > 2 else 1),
                ("sample2.jpg", "images/sample2.jpg", 1536000, "image/jpeg", ".jpg", 1, folder_ids[0] if len(folder_ids) > 0 else 1),
                ("logo.ai", "design/logo.ai", 3072000, "application/illustrator", ".ai", 1, folder_ids[1] if len(folder_ids) > 1 else 1),
            ]

            for filename, rel_path, size, mime_type, ext, is_image, folder_id in sample_files:
                cursor.execute("""
                    INSERT INTO shared_files (
                        folder_id, filename, relative_path, file_size,
                        mime_type, extension, is_image, has_thumbnail,
                        file_modified, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    folder_id, filename, rel_path, size, mime_type, ext,
                    is_image, is_image, datetime.now().isoformat(), datetime.now().isoformat()
                ))

            print(f"创建了 {len(sample_files)} 个示例文件记录")
        else:
            print("文件记录已存在，跳过创建")

        connection.commit()
        print("示例数据创建完成")

    except Exception as e:
        print(f"创建示例数据失败: {e}")
        connection.rollback()

def create_database():
    """创建SQLite数据库"""
    try:
        # 确保数据目录存在
        db_dir = "data"
        if not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
        
        db_path = os.path.join(db_dir, "file_share_system.db")
        print(f"正在创建SQLite数据库: {db_path}")
        
        # 连接到SQLite数据库（如果不存在会自动创建）
        connection = sqlite3.connect(db_path)
        cursor = connection.cursor()
        
        print("SQLite数据库连接成功")
        
        # 启用外键约束
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # 创建基本表结构
        print("正在创建数据表...")
        
        # 用户表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                salt TEXT NOT NULL,
                email TEXT UNIQUE,
                full_name TEXT,
                is_active INTEGER DEFAULT 1,
                is_admin INTEGER DEFAULT 0,
                is_banned INTEGER DEFAULT 0,
                ban_until TEXT,
                user_group TEXT DEFAULT 'user' CHECK (user_group IN ('admin', 'user', 'guest')),
                last_login TEXT,
                login_count INTEGER DEFAULT 0,
                failed_login_attempts INTEGER DEFAULT 0,
                last_failed_login TEXT,
                session_token TEXT,
                session_expires TEXT,
                download_count INTEGER DEFAULT 0,
                upload_count INTEGER DEFAULT 0,
                search_count INTEGER DEFAULT 0,
                download_quota INTEGER DEFAULT 0,
                used_quota INTEGER DEFAULT 0,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                notes TEXT
            )
        """)
        
        # 共享文件夹表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS shared_folders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                path TEXT NOT NULL,
                description TEXT,
                is_active INTEGER DEFAULT 1,
                allow_read INTEGER DEFAULT 1,
                allow_write INTEGER DEFAULT 0,
                allow_delete INTEGER DEFAULT 0,
                allow_upload INTEGER DEFAULT 0,
                allow_download INTEGER DEFAULT 1,
                allow_internal INTEGER DEFAULT 1,
                allow_external INTEGER DEFAULT 0,
                show_details INTEGER DEFAULT 1,
                enable_thumbnail INTEGER DEFAULT 1,
                max_file_size INTEGER DEFAULT 0,
                allowed_extensions TEXT,
                file_count INTEGER DEFAULT 0,
                total_size INTEGER DEFAULT 0,
                access_count INTEGER DEFAULT 0,
                download_count INTEGER DEFAULT 0,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                last_scanned TEXT
            )
        """)
        
        # 共享文件表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS shared_files (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                folder_id INTEGER NOT NULL,
                filename TEXT NOT NULL,
                relative_path TEXT NOT NULL,
                file_size INTEGER NOT NULL,
                file_hash TEXT,
                mime_type TEXT,
                extension TEXT,
                is_image INTEGER DEFAULT 0,
                is_video INTEGER DEFAULT 0,
                is_document INTEGER DEFAULT 0,
                has_thumbnail INTEGER DEFAULT 0,
                thumbnail_path TEXT,
                image_width INTEGER,
                image_height INTEGER,
                image_format TEXT,
                view_count INTEGER DEFAULT 0,
                download_count INTEGER DEFAULT 0,
                file_modified TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                last_accessed TEXT,
                FOREIGN KEY (folder_id) REFERENCES shared_folders(id) ON DELETE CASCADE
            )
        """)
        
        # 活动日志表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                details TEXT,
                ip_address TEXT,
                user_agent TEXT,
                success TEXT DEFAULT 'success',
                error_message TEXT,
                created_at TEXT DEFAULT (datetime('now'))
            )
        """)
        
        # 权限表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                permission_type TEXT DEFAULT 'file',
                created_at TEXT DEFAULT (datetime('now'))
            )
        """)
        
        # 用户权限关联表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                permission_id INTEGER NOT NULL,
                resource_type TEXT,
                resource_id INTEGER,
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT (datetime('now')),
                expires_at TEXT,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
            )
        """)

        # 用户收藏表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_favorites (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                file_id INTEGER NOT NULL,
                favorited_at TEXT DEFAULT (datetime('now')),
                notes TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (file_id) REFERENCES shared_files(id) ON DELETE CASCADE,
                UNIQUE(user_id, file_id)
            )
        """)

        # 收藏夹分组表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS favorite_folders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                color TEXT,
                icon TEXT,
                sort_order INTEGER DEFAULT 0,
                is_default INTEGER DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                created_at TEXT DEFAULT (datetime('now')),
                updated_at TEXT DEFAULT (datetime('now')),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        """)

        # 收藏夹分组项目关联表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS favorite_folder_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                folder_id INTEGER NOT NULL,
                favorite_id INTEGER NOT NULL,
                sort_order INTEGER DEFAULT 0,
                created_at TEXT DEFAULT (datetime('now')),
                FOREIGN KEY (folder_id) REFERENCES favorite_folders(id) ON DELETE CASCADE,
                FOREIGN KEY (favorite_id) REFERENCES user_favorites(id) ON DELETE CASCADE,
                UNIQUE(folder_id, favorite_id)
            )
        """)

        print("✅ 收藏功能表创建完成")

        # 下载记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS download_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_id INTEGER,
                folder_id INTEGER,
                user_id INTEGER,
                download_type TEXT NOT NULL,
                zip_filename TEXT NOT NULL,
                zip_path TEXT NOT NULL,
                file_size INTEGER NOT NULL,
                is_encrypted INTEGER DEFAULT 0,
                password TEXT,
                password_hint TEXT,
                download_status TEXT DEFAULT 'pending',
                expires_at TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                downloaded_at TEXT,
                FOREIGN KEY (file_id) REFERENCES shared_files(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
            )
        """)

        # 下载统计表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS download_statistics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_id INTEGER NOT NULL,
                total_downloads INTEGER DEFAULT 0,
                encrypted_downloads INTEGER DEFAULT 0,
                password_requests INTEGER DEFAULT 0,
                successful_requests INTEGER DEFAULT 0,
                first_download TEXT,
                last_download TEXT,
                last_password_request TEXT,
                updated_at TEXT DEFAULT (datetime('now')),
                FOREIGN KEY (file_id) REFERENCES shared_files(id) ON DELETE CASCADE,
                UNIQUE(file_id)
            )
        """)

        # 密码申请记录表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS password_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                file_id INTEGER NOT NULL,
                user_id INTEGER,
                download_record_id INTEGER,
                request_reason TEXT,
                request_ip TEXT,
                user_agent TEXT,
                status TEXT DEFAULT 'pending',
                approved_by INTEGER,
                approval_reason TEXT,
                password_provided TEXT,
                password_expires_at TEXT,
                created_at TEXT DEFAULT (datetime('now')),
                approved_at TEXT,
                FOREIGN KEY (file_id) REFERENCES shared_files(id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                FOREIGN KEY (download_record_id) REFERENCES download_records(id) ON DELETE SET NULL,
                FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
            )
        """)

        print("✅ 下载记录功能表创建完成")

        # 创建索引
        print("正在创建索引...")

        # 用户收藏索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_favorited ON user_favorites (user_id, favorited_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_file_favorited ON user_favorites (file_id, favorited_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_active_favorites ON user_favorites (is_active, favorited_at)")

        # 收藏夹分组索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_folder_name ON favorite_folders (user_id, name)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_sort ON favorite_folders (user_id, sort_order)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_folder_sort ON favorite_folder_items (folder_id, sort_order)")

        # 下载记录索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_file_download ON download_records (file_id, downloaded_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_folder_download ON download_records (folder_id, downloaded_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_download ON download_records (user_id, downloaded_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_download_status ON download_records (download_status, created_at)")

        # 密码申请索引
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_file_request ON password_requests (file_id, created_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_user_request ON password_requests (user_id, created_at)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_status_request ON password_requests (status, created_at)")

        print("✅ 索引创建完成")
        print("✅ 所有数据表创建成功")

        # 创建默认管理员用户
        print("正在创建默认管理员用户...")

        import hashlib
        import secrets

        # 生成密码哈希 - 使用简单方法
        password = "admin123"
        salt = secrets.token_hex(16)
        combined = password + salt
        password_hash = hashlib.sha256(combined.encode('utf-8')).hexdigest()

        # 检查管理员是否已存在
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        if not cursor.fetchone():
            cursor.execute("""
                INSERT INTO users (username, password_hash, salt, full_name, is_admin, user_group)
                VALUES (?, ?, ?, ?, ?, ?)
            """, ('admin', password_hash, salt, '系统管理员', 1, 'admin'))

            connection.commit()
            print("默认管理员用户创建成功")
            print("用户名: admin")
            print("密码: admin123")
        else:
            print("管理员用户已存在")

        # 检查并创建示例文件数据（用于测试收藏功能）
        print("\n正在创建示例文件数据...")
        create_sample_data(cursor, connection)

        connection.close()
        print("\n数据库初始化完成！")
        return True

    except Exception as e:
        print(f"数据库初始化失败: {e}")
        return False

def main():
    """主函数"""
    print("企业级文件共享系统 - SQLite数据库初始化")
    print("=" * 50)

    if create_database():
        print("\n✓ 数据库初始化成功，现在可以启动服务器了")
        return 0
    else:
        print("\n✗ 数据库初始化失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
