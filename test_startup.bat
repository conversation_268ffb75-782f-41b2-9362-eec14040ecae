@echo off
title File Share System - Test
chcp 65001 >nul

echo.
echo ========================================
echo   File Share System - Test Version
echo ========================================
echo.

:: Check Python installation
echo [1/4] Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found, please install Python 3.7+
    echo.
    echo Download: https://www.python.org/downloads/
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo OK - Python environment check passed (version: %PYTHON_VERSION%)

:: Check if in correct directory
echo [2/4] Checking project directory...
if not exist "backend\main.py" (
    echo Error: Please run this script from project root directory
    pause
    exit /b 1
)
echo OK - Project directory check passed

:: Configure pip mirrors for faster download
echo [3/4] Configuring dependency sources...
set PIP_MIRRORS=-i https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn
echo OK - Configured Tsinghua University mirror

:: Check and install dependencies intelligently
echo [4/4] Smart dependency check and installation...
echo.

:: Use the advanced dependency checker
python check_dependencies.py
set DEPS_CHECK_RESULT=%errorlevel%

if %DEPS_CHECK_RESULT% equ 0 (
    echo.
    echo OK - All dependencies are correctly installed
) else (
    echo.
    echo WARNING - Missing or mismatched dependencies detected, starting installation...
    echo.
    
    :: Try primary mirror first
    echo Installing dependencies from Tsinghua mirror...
    pip install -r backend\requirements.txt %PIP_MIRRORS% --upgrade
    
    if errorlevel 1 (
        echo.
        echo WARNING - Tsinghua mirror failed, trying Aliyun mirror...
        pip install -r backend\requirements.txt -i https://mirrors.aliyun.com/pypi/simple --trusted-host mirrors.aliyun.com --upgrade
        
        if errorlevel 1 (
            echo.
            echo WARNING - Aliyun mirror failed, trying Douban mirror...
            pip install -r backend\requirements.txt -i https://pypi.douban.com/simple --trusted-host pypi.douban.com --upgrade
            
            if errorlevel 1 (
                echo.
                echo ERROR - All domestic mirrors failed, trying official source...
                pip install -r backend\requirements.txt --upgrade
                
                if errorlevel 1 (
                    echo.
                    echo ERROR - Dependency installation failed. Possible solutions:
                    echo 1. Check network connection
                    echo 2. Try manual run: pip install -r backend\requirements.txt
                    echo 3. Check Python and pip versions
                    echo 4. Try using virtual environment
                    echo.
                    pause
                    exit /b 1
                )
            )
        )
    )
    
    echo.
    echo OK - Dependency installation completed
)

echo.
echo ========================================
echo        System Initialization
echo ========================================

:: Check if database exists
echo [5/6] Checking database status...
if not exist "backend\data\file_share_system.db" (
    echo WARNING - First run, initializing SQLite database...
    cd backend
    python init_database_sqlite.py
    cd ..

    if errorlevel 1 (
        echo ERROR - Database initialization failed
        echo.
        echo Possible solutions:
        echo 1. Check if backend\data directory exists
        echo 2. Check if there is enough disk space
        echo 3. Check file permissions
        pause
        exit /b 1
    )

    echo OK - Database initialization successful
) else (
    echo OK - Database already exists, skipping initialization
)

:: Final dependency verification before starting
echo [6/6] Final check before startup...
python -c "import flask, sqlalchemy, PIL, requests; print('OK - Core dependencies verified')" 2>nul
if errorlevel 1 (
    echo ERROR - Core dependency verification failed, please re-run script
    pause
    exit /b 1
)

echo.
echo ========================================
echo      Starting File Share System
echo ========================================
echo.
echo Default admin account:
echo   Username: admin
echo   Password: admin123
echo.
echo System will auto-open browser, if not, please visit:
echo   Frontend: http://localhost:8082
echo   API: http://localhost:8086
echo.
echo Press Ctrl+C to stop server
echo.
echo Starting server...

cd backend
python main.py

:: If program exits with error, show error info
if errorlevel 1 (
    echo.
    echo ERROR - Server startup failed, please check error messages
    echo.
    echo Common solutions:
    echo 1. Check if ports 8082 and 8086 are occupied
    echo    - Use command: netstat -ano ^| findstr "8082\|8086"
    echo 2. Check if Python dependencies are correctly installed
    echo    - Re-run this script for dependency check
    echo 3. Check if database file is normal
    echo    - Delete backend\data\file_share_system.db to reinitialize
    echo 4. Check firewall settings
    echo 5. View detailed logs: backend\logs\ directory
    echo.
    echo For technical support, please provide log files from backend\logs\
    echo.
    pause
)

cd ..

echo.
echo ========================================
echo         Program Exited
echo ========================================
