#!/usr/bin/env python3
"""
局域网访问诊断工具
"""

import socket
import subprocess
import platform
import requests
import time
import json

def check_server_status():
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    
    ports = [8084, 8086]
    results = {}
    
    for port in ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"✅ 端口 {port} 正在监听")
                results[port] = True
            else:
                print(f"❌ 端口 {port} 未监听")
                results[port] = False
        except Exception as e:
            print(f"❌ 检查端口 {port} 失败: {e}")
            results[port] = False
    
    return results

def get_network_interfaces():
    """获取网络接口信息"""
    print("\n🌐 获取网络接口信息...")
    
    interfaces = []
    
    try:
        if platform.system() == "Windows":
            result = subprocess.run(['ipconfig'], capture_output=True, text=True, encoding='gbk')
            lines = result.stdout.split('\n')
            
            current_adapter = ""
            for line in lines:
                line = line.strip()
                if "适配器" in line:
                    current_adapter = line
                elif "IPv4" in line and ":" in line:
                    ip = line.split(':')[-1].strip()
                    if ip and not ip.startswith('127.') and not ip.startswith('169.254.'):
                        interfaces.append({
                            'name': current_adapter,
                            'ip': ip
                        })
                        print(f"📍 {current_adapter}: {ip}")
        else:
            result = subprocess.run(['hostname', '-I'], capture_output=True, text=True)
            if result.returncode == 0:
                ips = result.stdout.strip().split()
                for ip in ips:
                    if not ip.startswith('127.') and not ip.startswith('169.254.'):
                        interfaces.append({
                            'name': 'Network Interface',
                            'ip': ip
                        })
                        print(f"📍 Network Interface: {ip}")
    except Exception as e:
        print(f"❌ 获取网络接口失败: {e}")
    
    return interfaces

def test_local_access():
    """测试本地访问"""
    print("\n🏠 测试本地访问...")
    
    urls = [
        'http://localhost:8084',
        'http://localhost:8086/api/health',
        'http://127.0.0.1:8084',
        'http://127.0.0.1:8086/api/health'
    ]
    
    for url in urls:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {url} - 访问成功")
            else:
                print(f"⚠️ {url} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ {url} - 访问失败: {e}")

def test_lan_access(interfaces):
    """测试局域网访问"""
    print("\n🌐 测试局域网访问...")
    
    for interface in interfaces:
        ip = interface['ip']
        print(f"\n测试 {interface['name']} ({ip}):")
        
        # 测试前端
        frontend_url = f"http://{ip}:8084"
        try:
            response = requests.get(frontend_url, timeout=10)
            if response.status_code == 200:
                print(f"✅ 前端访问成功: {frontend_url}")
            else:
                print(f"⚠️ 前端访问异常: {frontend_url} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ 前端访问失败: {frontend_url} - {e}")
        
        # 测试API
        api_url = f"http://{ip}:8086/api/health"
        try:
            response = requests.get(api_url, timeout=10)
            if response.status_code == 200:
                print(f"✅ API访问成功: {api_url}")
            else:
                print(f"⚠️ API访问异常: {api_url} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"❌ API访问失败: {api_url} - {e}")

def check_firewall_rules():
    """检查防火墙规则"""
    print("\n🔥 检查防火墙规则...")
    
    if platform.system() == "Windows":
        try:
            # 检查入站规则
            result = subprocess.run([
                'netsh', 'advfirewall', 'firewall', 'show', 'rule', 
                'name=文件共享系统-前端'
            ], capture_output=True, text=True, encoding='gbk')
            
            if "文件共享系统-前端" in result.stdout:
                print("✅ 前端防火墙规则已配置")
            else:
                print("❌ 前端防火墙规则未配置")
            
            result = subprocess.run([
                'netsh', 'advfirewall', 'firewall', 'show', 'rule', 
                'name=文件共享系统-API'
            ], capture_output=True, text=True, encoding='gbk')
            
            if "文件共享系统-API" in result.stdout:
                print("✅ API防火墙规则已配置")
            else:
                print("❌ API防火墙规则未配置")
                
        except Exception as e:
            print(f"❌ 检查防火墙规则失败: {e}")
    else:
        print("ℹ️ 非Windows系统，跳过防火墙检查")

def check_port_binding():
    """检查端口绑定"""
    print("\n🔌 检查端口绑定...")
    
    try:
        if platform.system() == "Windows":
            result = subprocess.run(['netstat', '-an'], capture_output=True, text=True)
        else:
            result = subprocess.run(['netstat', '-ln'], capture_output=True, text=True)
        
        lines = result.stdout.split('\n')
        
        found_8084 = False
        found_8086 = False
        
        for line in lines:
            if ':8084' in line and 'LISTENING' in line:
                print(f"✅ 端口 8084 绑定: {line.strip()}")
                if '0.0.0.0:8084' in line:
                    print("✅ 前端服务器正确绑定到所有接口")
                found_8084 = True
            elif ':8086' in line and 'LISTENING' in line:
                print(f"✅ 端口 8086 绑定: {line.strip()}")
                if '0.0.0.0:8086' in line:
                    print("✅ API服务器正确绑定到所有接口")
                found_8086 = True
        
        if not found_8084:
            print("❌ 端口 8084 未正确绑定")
        if not found_8086:
            print("❌ 端口 8086 未正确绑定")
            
    except Exception as e:
        print(f"❌ 检查端口绑定失败: {e}")

def ping_test(interfaces):
    """Ping测试"""
    print("\n🏓 Ping测试...")
    
    for interface in interfaces:
        ip = interface['ip']
        try:
            if platform.system() == "Windows":
                result = subprocess.run(['ping', '-n', '1', ip], 
                                      capture_output=True, text=True, timeout=10)
            else:
                result = subprocess.run(['ping', '-c', '1', ip], 
                                      capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print(f"✅ Ping {ip} 成功")
            else:
                print(f"❌ Ping {ip} 失败")
        except Exception as e:
            print(f"❌ Ping {ip} 异常: {e}")

def generate_fix_suggestions(server_status, interfaces):
    """生成修复建议"""
    print("\n🔧 修复建议:")
    print("=" * 50)
    
    # 检查服务器状态
    if not server_status.get(8084, False):
        print("❌ 前端服务器未运行")
        print("   建议: 重新启动服务器")
    
    if not server_status.get(8086, False):
        print("❌ API服务器未运行")
        print("   建议: 重新启动服务器")
    
    # 检查网络接口
    if not interfaces:
        print("❌ 未找到可用的网络接口")
        print("   建议: 检查网络连接")
    
    # 通用建议
    print("\n📋 通用解决方案:")
    print("1. 运行防火墙配置脚本:")
    print("   右键 '配置防火墙.bat' -> '以管理员身份运行'")
    print("\n2. 检查杀毒软件设置:")
    print("   将程序添加到杀毒软件白名单")
    print("\n3. 检查路由器设置:")
    print("   确保路由器允许内网设备互相通信")
    print("\n4. 重启网络服务:")
    print("   重启网络适配器或重启计算机")
    print("\n5. 使用正确的IP地址:")
    if interfaces:
        print("   推荐使用以下地址:")
        for interface in interfaces:
            ip = interface['ip']
            if ip.startswith('192.168.') or ip.startswith('10.') or ip.startswith('172.'):
                print(f"   http://{ip}:8084")

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 局域网访问诊断工具")
    print("=" * 60)
    
    # 1. 检查服务器状态
    server_status = check_server_status()
    
    # 2. 获取网络接口
    interfaces = get_network_interfaces()
    
    # 3. 测试本地访问
    test_local_access()
    
    # 4. 测试局域网访问
    if interfaces:
        test_lan_access(interfaces)
    
    # 5. 检查防火墙规则
    check_firewall_rules()
    
    # 6. 检查端口绑定
    check_port_binding()
    
    # 7. Ping测试
    if interfaces:
        ping_test(interfaces)
    
    # 8. 生成修复建议
    generate_fix_suggestions(server_status, interfaces)
    
    print("\n" + "=" * 60)
    print("🏁 诊断完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
