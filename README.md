# 🌟 文件共享系统 - SQLite数据库版

## 📋 更新说明

本次更新将数据库从MySQL迁移到了SQLite，提供了更简单、更便携的部署体验。

### ✨ 新功能特性

- **🗄️ SQLite数据库**: 轻量级数据库，无需额外安装MySQL服务
- **🚀 自动初始化**: 系统首次启动时自动创建数据库和表结构
- **📦 便携部署**: 数据库文件随项目一起，便于备份和迁移
- **🔄 跨设备同步**: 用户在不同设备上登录可看到相同的收藏
- **📊 统计分析**: 支持收藏行为统计和分析
- **⚡ 批量操作**: 支持批量检查收藏状态，提升性能
- **🔒 数据安全**: 服务端验证，防止数据篡改
- **📝 收藏备注**: 支持为收藏添加个人备注

## 🚀 快速开始

### 方式一：一键启动（推荐）

1. **双击运行** `一键启动_SQLite.bat`
2. 系统会自动：
   - 检查Python环境
   - 安装必要依赖
   - 自动创建SQLite数据库
   - 创建示例数据
   - 启动服务器

### 方式二：手动启动

1. **安装依赖**
   ```bash
   pip install -r backend/requirements.txt
   ```

2. **初始化数据库（首次运行）**
   ```bash
   cd backend
   python init_database_sqlite.py
   ```

3. **启动服务器**
   ```bash
   python main.py
   ```

## 🌐 访问地址

- **前端界面**: http://localhost:8082
- **API接口**: http://localhost:8086
- **管理员账户**: admin / admin123

## 🎯 系统特性

### 🔐 用户管理
- 默认管理员账户：`admin` / `admin123`
- 支持用户注册、登录、权限管理
- 会话管理和安全验证

### 📁 文件管理
- 支持多种文件格式（图片、文档、压缩包等）
- 智能缩略图生成
- 文件搜索和过滤
- 批量操作支持

### ⭐ 收藏功能
- **SQLite存储**：收藏数据安全存储在SQLite数据库中
- **收藏管理**：添加、删除、查看收藏文件
- **收藏分组**：支持创建自定义收藏夹分组
- **批量操作**：支持批量收藏和取消收藏
- **收藏备注**：为收藏项添加个人备注
- **收藏统计**：查看收藏数量和统计信息

### 📥 下载功能
- 单文件下载
- 批量文件下载
- 文件夹打包下载
- 下载记录追踪
- 自动加密保护（超过限制次数）
- 密码申请系统

### 🔍 搜索功能
- 文件名搜索
- 支持模糊匹配
- 实时搜索结果

### 📊 监控统计
- 实时系统状态监控
- 用户活动日志
- 下载统计分析
- 系统性能指标

### 🗄️ 数据库特性
- **SQLite数据库**：轻量级、无需额外安装
- **自动初始化**：首次启动自动创建数据库
- **数据备份**：简单的文件复制即可备份
- **便携部署**：数据库文件随项目一起

## 📚 API文档



## 🔧 技术架构

### 后端
- **Python 3.7+**: 主要开发语言
- **SQLite**: 轻量级数据库存储
- **Flask**: Web框架
- **SQLAlchemy**: ORM数据库操作

### 前端
- **原生JavaScript**: 无框架依赖
- **HTML5 + CSS3**: 现代化UI
- **Fetch API**: 异步数据请求

### 数据库表结构



## 🔄 迁移说明

### 从MySQL迁移到SQLite

如果您之前使用了MySQL版本：

1. **备份MySQL数据**
   - 导出现有MySQL数据
   - 保存重要的用户数据和收藏记录

2. **自动迁移**
   - 删除旧的数据库配置
   - 运行 `backend/init_database_sqlite.py` 创建新数据库
   - 或使用一键启动脚本自动处理

3. **数据导入**
   - 如需保留原有数据，可手动导入到SQLite
   - 系统会自动创建示例数据用于测试

### 向后兼容

- 前端代码保留了localStorage的回退机制
- 如果API调用失败，会自动使用本地存储
- 提供了完整的数据库初始化工具

## 🛠️ 开发说明

### 项目结构
```
├── backend/                    # 后端代码
│   ├── data/                  # 数据库文件目录
│   │   └── file_share_system.db  # SQLite数据库文件
│   ├── models/                # 数据模型
│   │   ├── favorite.py        # 收藏模型
│   │   ├── user.py           # 用户模型
│   │   └── file_share.py     # 文件共享模型
│   ├── services/              # 业务服务
│   │   ├── favorite_service.py          # 完整收藏服务
│   │   └── favorite_service_simple.py  # 简化收藏服务
│   ├── api/                   # API接口
│   ├── config/                # 配置文件
│   │   ├── database.py       # 数据库配置
│   │   └── settings.py       # 系统设置
│   ├── init_database_sqlite.py  # SQLite数据库初始化
│   └── main.py               # 主程序
├── frontend/                  # 前端代码
│   ├── js/
│   │   ├── api.js            # API封装
│   │   └── file-manager.js   # 文件管理器
│   └── export-favorites.html # 数据导出工具
├── 一键启动_SQLite.bat       # SQLite版启动脚本
├── check_db.py               # 数据库检查工具
└── README.md                 # 说明文档
```

### 添加新功能

1. **后端API**
   - 在 `backend/api/server.py` 中添加路由
   - 在 `backend/services/favorite_service_simple.py` 中添加业务逻辑

2. **前端调用**
   - 在 `frontend/js/api.js` 中添加API方法
   - 在 `frontend/js/file-manager.js` 中调用API

## 🐛 故障排除

### 常见问题

1. **数据库初始化失败**
   - 检查 `backend/data/` 目录是否有写入权限
   - 确认Python版本为3.7或更高
   - 运行 `python backend/init_database_sqlite.py` 手动初始化

2. **Python依赖缺失**
   ```bash
   pip install -r backend/requirements.txt
   ```

3. **端口占用**
   - 前端端口8082被占用：修改 `backend/main.py` 中的端口配置
   - API端口8086被占用：修改 `backend/config/settings.py` 中的端口配置

4. **收藏功能不工作**
   - 检查浏览器控制台错误
   - 确认API服务器正常运行
   - 检查用户登录状态
   - 运行 `python check_db.py` 检查数据库状态

5. **数据库文件损坏**
   - 删除 `backend/data/file_share_system.db`
   - 重新运行初始化脚本

### 日志查看

- **后端日志**: 控制台输出
- **前端日志**: 浏览器开发者工具 Console
- **数据库检查**: 运行 `python check_db.py`

## 📞 技术支持

如果遇到问题，请：

1. 查看控制台错误信息
2. 运行 `python check_db.py` 检查数据库状态
3. 确认SQLite数据库文件是否正常创建
4. 验证API接口是否正常响应（访问 http://localhost:8086/api/health）

## 🎯 未来计划

- [ ] 收藏夹分组功能
- [ ] 收藏导入/导出
- [ ] 收藏分享功能
- [ ] 智能推荐收藏
- [ ] 收藏标签系统
- [ ] 数据库迁移工具
- [ ] 性能优化

---

**🎉 享受全新的SQLite数据库体验！**
