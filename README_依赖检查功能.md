# 文件共享系统 - 智能依赖检查和安装功能

## 功能概述

我们为文件共享系统的启动脚本添加了智能依赖检查和自动安装功能，确保程序启动时所有必需的 Python 依赖包都已正确安装。

## 主要特性

### 1. 智能依赖检查
- ✅ **详细包状态检查**：检查每个依赖包的安装状态和版本
- ✅ **版本匹配验证**：确保已安装包的版本符合 requirements.txt 要求
- ✅ **清晰的状态报告**：区分缺失包、版本不匹配包和正确安装的包
- ✅ **多编码支持**：自动处理不同编码格式的 requirements.txt 文件

### 2. 国内镜像源配置
- ✅ **多镜像源支持**：
  - 主源：清华大学镜像 (https://pypi.tuna.tsinghua.edu.cn/simple)
  - 备用源1：阿里云镜像 (https://mirrors.aliyun.com/pypi/simple)
  - 备用源2：豆瓣镜像 (https://pypi.douban.com/simple)
  - 最后备用：官方源 (https://pypi.org/simple)
- ✅ **自动故障转移**：当一个镜像源失败时，自动切换到下一个
- ✅ **加速下载**：使用国内镜像源显著提升下载速度

### 3. 用户友好的界面
- ✅ **进度显示**：显示检查和安装的详细进度
- ✅ **状态图标**：使用 ✅ ❌ ⚠️ 等图标清晰显示状态
- ✅ **详细错误信息**：提供具体的错误信息和解决建议
- ✅ **安装建议**：自动生成安装命令供手动执行

### 4. 错误处理和恢复
- ✅ **多重重试机制**：在不同镜像源之间自动重试
- ✅ **详细错误报告**：显示具体的错误原因和解决方案
- ✅ **优雅降级**：即使部分依赖安装失败，也会尝试继续运行

## 文件结构

```
项目根目录/
├── 一键启动_SQLite.bat          # 主启动脚本（已优化）
├── test_startup.bat             # 测试版启动脚本
├── check_dependencies.py        # 智能依赖检查脚本
├── test_dependency_check.py     # 依赖检查测试脚本
└── README_依赖检查功能.md       # 本文档
```

## 使用方法

### 方法1：使用主启动脚本
```bash
# 双击运行或在命令行执行
一键启动_SQLite.bat
```

### 方法2：使用测试版脚本（英文界面）
```bash
# 在命令行执行
cmd /c test_startup.bat
```

### 方法3：单独运行依赖检查
```bash
# 只检查依赖状态，不安装
python check_dependencies.py
```

## 工作流程

1. **环境检查**
   - 检查 Python 是否已安装
   - 验证项目目录结构
   - 配置国内镜像源

2. **依赖分析**
   - 解析 backend/requirements.txt 文件
   - 检查每个包的安装状态和版本
   - 生成详细的状态报告

3. **智能安装**
   - 如果所有依赖都正确安装，跳过安装步骤
   - 如果发现问题，自动从国内镜像源安装
   - 支持多镜像源故障转移

4. **系统启动**
   - 初始化数据库（如果需要）
   - 进行最终依赖验证
   - 启动文件共享系统

## 依赖检查输出示例

```
File Share System - Dependency Checker
============================================================
Checking project dependencies...
============================================================
  Flask                : OK - Installed (version: 2.3.3)
  Flask-CORS           : VERSION MISMATCH (current: 6.0.0, required: 4.0.0)
  Flask-SocketIO       : OK - Installed (version: 5.3.6)
  SQLAlchemy           : MISSING - Not installed
  Pillow               : VERSION MISMATCH (current: 10.4.0, required: 10.1.0)
  opencv-python        : MISSING - Not installed
  ...
============================================================

Dependency Check Summary:
  OK: 2 packages
  Missing: 8 packages
  Version mismatch: 5 packages

Missing packages:
  - SQLAlchemy==2.0.23
  - opencv-python==********
  ...

Version mismatch packages:
  - Flask-CORS: 6.0.0 -> 4.0.0
  - Pillow: 10.4.0 -> 10.1.0
  ...
```

## 技术特点

### 1. 智能包名映射
- 自动处理包名和导入名的差异（如 Pillow -> PIL）
- 支持特殊包名转换（如 opencv-python -> cv2）

### 2. 版本检测机制
- 优先使用模块的 `__version__` 属性
- 备用使用 `pkg_resources` 获取版本信息
- 处理无版本信息的包

### 3. 编码兼容性
- 支持 UTF-8、GBK、CP1252、Latin1 等多种编码
- 自动检测和处理编码问题

### 4. 错误恢复策略
- 网络错误时自动重试
- 镜像源故障时自动切换
- 提供详细的故障排除指导

## 配置选项

### 镜像源配置
可以在批处理文件中修改镜像源设置：
```batch
set PIP_MIRRORS=-i https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn
```

### 依赖文件路径
可以在 check_dependencies.py 中修改依赖文件路径：
```python
checker = DependencyChecker("backend/requirements.txt")
```

## 故障排除

### 常见问题

1. **编码错误**
   - 确保 requirements.txt 文件使用 UTF-8 编码
   - 脚本会自动尝试多种编码格式

2. **网络连接问题**
   - 脚本会自动尝试多个国内镜像源
   - 如果所有镜像源都失败，会尝试官方源

3. **权限问题**
   - 确保有足够的权限安装 Python 包
   - 考虑使用虚拟环境

4. **Python 版本兼容性**
   - 确保使用 Python 3.7 或更高版本
   - 某些包可能需要特定的 Python 版本

### 手动安装
如果自动安装失败，可以使用生成的安装命令手动安装：
```bash
pip install SQLAlchemy==2.0.23 opencv-python==******** ...
```

## 更新日志

### v1.0 (当前版本)
- ✅ 实现智能依赖检查功能
- ✅ 添加国内镜像源支持
- ✅ 实现多镜像源故障转移
- ✅ 添加详细的状态报告
- ✅ 优化用户界面和错误处理

## 贡献

如需改进此功能，请：
1. 测试不同的依赖配置
2. 添加更多镜像源选项
3. 改进错误处理机制
4. 优化用户界面显示
