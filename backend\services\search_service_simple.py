#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化搜索服务 - 不依赖外部库的基础版本
"""

import os
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
import threading
import time
import re

from models.file_share import SharedFile, SharedFolder
from utils.logger import setup_logger

class SimpleTextSearchEngine:
    """简化文本搜索引擎"""

    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = setup_logger("SimpleTextSearchEngine")

        # 使用内存缓存文件索引（从数据库加载）
        self.file_index = {}

        # 加载现有索引
        self.load_index()
    
    def load_index(self):
        """从数据库加载索引"""
        try:
            if not self.db_manager:
                return

            with self.db_manager.get_session() as session:
                # 查询文本索引
                result = session.execute("""
                    SELECT file_id, index_data
                    FROM search_index
                    WHERE index_type = 'text'
                """)

                for row in result:
                    file_id, index_data = row
                    try:
                        self.file_index[str(file_id)] = json.loads(index_data)
                    except json.JSONDecodeError:
                        self.logger.warning(f"无法解析文件 {file_id} 的索引数据")

                self.logger.info(f"从数据库加载文本索引: {len(self.file_index)} 个文件")
        except Exception as e:
            self.logger.error(f"加载索引失败: {e}")
            self.file_index = {}
    
    def save_index_to_db(self, file_id: str, index_data: dict):
        """保存单个文件索引到数据库"""
        try:
            if not self.db_manager:
                return

            with self.db_manager.get_session() as session:
                # 删除旧索引
                session.execute("""
                    DELETE FROM search_index
                    WHERE file_id = ? AND index_type = 'text'
                """, (int(file_id),))

                # 插入新索引
                session.execute("""
                    INSERT INTO search_index (file_id, index_type, index_data)
                    VALUES (?, 'text', ?)
                """, (int(file_id), json.dumps(index_data, ensure_ascii=False)))

        except Exception as e:
            self.logger.error(f"保存索引到数据库失败: {e}")
    
    def add_file_to_index(self, file_record: SharedFile):
        """添加文件到索引"""
        try:
            index_data = {
                'id': file_record.id,
                'filename': file_record.filename,
                'path': file_record.relative_path,
                'extension': file_record.extension or "",
                'size': file_record.file_size,
                'modified': file_record.file_modified.isoformat() if file_record.file_modified else None,
                'folder_id': file_record.folder_id
            }

            # 更新内存索引
            self.file_index[str(file_record.id)] = index_data

            # 保存到数据库
            self.save_index_to_db(str(file_record.id), index_data)

        except Exception as e:
            self.logger.error(f"添加文件到索引失败: {e}")
    
    def remove_file_from_index(self, file_id: int):
        """从索引中移除文件"""
        try:
            file_id_str = str(file_id)

            # 从内存索引中移除
            if file_id_str in self.file_index:
                del self.file_index[file_id_str]

            # 从数据库中移除
            if self.db_manager:
                with self.db_manager.get_session() as session:
                    session.execute("""
                        DELETE FROM search_index
                        WHERE file_id = ? AND index_type = 'text'
                    """, (file_id,))

        except Exception as e:
            self.logger.error(f"从索引移除文件失败: {e}")
    
    def search(self, query: str, limit: int = 100) -> List[Dict[str, Any]]:
        """搜索文件"""
        try:
            if not query.strip():
                # 返回所有文件
                results = list(self.file_index.values())
            else:
                # 简单的文件名匹配搜索
                query_lower = query.lower()
                results = []
                
                for file_data in self.file_index.values():
                    filename_lower = file_data['filename'].lower()
                    path_lower = file_data['path'].lower()
                    
                    # 支持通配符搜索
                    if '*' in query_lower or '?' in query_lower:
                        # 转换为正则表达式
                        pattern = query_lower.replace('*', '.*').replace('?', '.')
                        if re.search(pattern, filename_lower) or re.search(pattern, path_lower):
                            results.append(file_data)
                    else:
                        # 简单包含搜索
                        if query_lower in filename_lower or query_lower in path_lower:
                            results.append(file_data)
            
            # 限制结果数量
            return results[:limit]
            
        except Exception as e:
            self.logger.error(f"文本搜索失败: {e}")
            return []
    
    def rebuild_index(self, db_manager):
        """重建索引"""
        try:
            self.logger.info("开始重建文本搜索索引")
            
            # 清空现有索引
            self.file_index = {}
            
            # 重新添加所有文件
            with db_manager.get_session() as session:
                files = session.query(SharedFile).all()
                
                for file_record in files:
                    self.add_file_to_index(file_record)
            
            self.save_index()
            self.logger.info("文本搜索索引重建完成")
            
        except Exception as e:
            self.logger.error(f"重建文本搜索索引失败: {e}")

class SimpleImageSearchEngine:
    """简化图像搜索引擎"""

    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = setup_logger("SimpleImageSearchEngine")

        # 简化的图像索引（基于文件名和属性）
        self.image_index = {}

        self.load_index()
    
    def load_index(self):
        """从数据库加载图像索引"""
        try:
            if not self.db_manager:
                return

            with self.db_manager.get_session() as session:
                # 查询图像索引
                result = session.execute("""
                    SELECT file_id, index_data
                    FROM search_index
                    WHERE index_type = 'image'
                """)

                for row in result:
                    file_id, index_data = row
                    try:
                        self.image_index[str(file_id)] = json.loads(index_data)
                    except json.JSONDecodeError:
                        self.logger.warning(f"无法解析图像 {file_id} 的索引数据")

                self.logger.info(f"从数据库加载图像索引: {len(self.image_index)} 个图像")
        except Exception as e:
            self.logger.error(f"加载图像索引失败: {e}")
            self.image_index = {}
    
    def save_index_to_db(self, file_id: str, index_data: dict):
        """保存单个图像索引到数据库"""
        try:
            if not self.db_manager:
                return

            with self.db_manager.get_session() as session:
                # 删除旧索引
                session.execute("""
                    DELETE FROM search_index
                    WHERE file_id = ? AND index_type = 'image'
                """, (int(file_id),))

                # 插入新索引
                session.execute("""
                    INSERT INTO search_index (file_id, index_type, index_data)
                    VALUES (?, 'image', ?)
                """, (int(file_id), json.dumps(index_data, ensure_ascii=False)))

        except Exception as e:
            self.logger.error(f"保存图像索引到数据库失败: {e}")
    
    def add_image_to_index(self, file_record: SharedFile, image_path: str):
        """添加图像到索引"""
        try:
            if not file_record.is_image:
                return

            # 获取基本图像信息
            try:
                stat = os.stat(image_path)
                file_size = stat.st_size
            except:
                file_size = file_record.file_size

            index_data = {
                'id': file_record.id,
                'filename': file_record.filename,
                'path': file_record.relative_path,
                'folder_id': file_record.folder_id,
                'size': file_size,
                'width': file_record.image_width,
                'height': file_record.image_height,
                'format': file_record.image_format
            }

            # 更新内存索引
            self.image_index[str(file_record.id)] = index_data

            # 保存到数据库
            self.save_index_to_db(str(file_record.id), index_data)

        except Exception as e:
            self.logger.error(f"添加图像到索引失败: {e}")
    
    def remove_image_from_index(self, file_id: int):
        """从索引中移除图像"""
        try:
            file_id_str = str(file_id)

            # 从内存索引中移除
            if file_id_str in self.image_index:
                del self.image_index[file_id_str]

            # 从数据库中移除
            if self.db_manager:
                with self.db_manager.get_session() as session:
                    session.execute("""
                        DELETE FROM search_index
                        WHERE file_id = ? AND index_type = 'image'
                    """, (file_id,))

        except Exception as e:
            self.logger.error(f"从索引移除图像失败: {e}")
    
    def search_similar_images(self, query_image_path: str, 
                            limit: int = 50, threshold: float = 0.7) -> List[Dict[str, Any]]:
        """搜索相似图像（简化版本）"""
        try:
            # 简化版本：基于文件名和尺寸进行匹配
            results = []
            
            # 获取查询图像的基本信息
            try:
                query_filename = os.path.basename(query_image_path)
                query_name, query_ext = os.path.splitext(query_filename)
                
                for image_data in self.image_index.values():
                    similarity = 0.0
                    
                    # 基于文件扩展名的相似度
                    file_ext = os.path.splitext(image_data['filename'])[1]
                    if file_ext.lower() == query_ext.lower():
                        similarity += 0.3
                    
                    # 基于文件名的相似度
                    filename_similarity = self.calculate_string_similarity(
                        query_name.lower(), 
                        os.path.splitext(image_data['filename'])[0].lower()
                    )
                    similarity += filename_similarity * 0.7
                    
                    if similarity >= threshold:
                        result = image_data.copy()
                        result['similarity'] = similarity
                        results.append(result)
                
                # 按相似度排序
                results.sort(key=lambda x: x['similarity'], reverse=True)
                return results[:limit]
                
            except Exception as e:
                self.logger.error(f"处理查询图像失败: {e}")
                return []
            
        except Exception as e:
            self.logger.error(f"图像搜索失败: {e}")
            return []
    
    def calculate_string_similarity(self, str1: str, str2: str) -> float:
        """计算字符串相似度"""
        try:
            # 简单的字符串相似度计算
            if str1 == str2:
                return 1.0
            
            if not str1 or not str2:
                return 0.0
            
            # 计算最长公共子序列
            len1, len2 = len(str1), len(str2)
            dp = [[0] * (len2 + 1) for _ in range(len1 + 1)]
            
            for i in range(1, len1 + 1):
                for j in range(1, len2 + 1):
                    if str1[i-1] == str2[j-1]:
                        dp[i][j] = dp[i-1][j-1] + 1
                    else:
                        dp[i][j] = max(dp[i-1][j], dp[i][j-1])
            
            lcs_length = dp[len1][len2]
            similarity = (2.0 * lcs_length) / (len1 + len2)
            
            return similarity
            
        except Exception as e:
            return 0.0

class SimpleSearchService:
    """简化搜索服务"""

    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = setup_logger("SimpleSearchService")

        # 初始化搜索引擎
        self.text_engine = SimpleTextSearchEngine(db_manager)
        self.image_engine = SimpleImageSearchEngine(db_manager)
        
        # 搜索统计
        self.search_stats = {
            'total_searches': 0,
            'text_searches': 0,
            'image_searches': 0
        }
    
    def search_text(self, query: str, limit: int = 100) -> List[Dict[str, Any]]:
        """文本搜索"""
        try:
            self.search_stats['total_searches'] += 1
            self.search_stats['text_searches'] += 1
            
            results = self.text_engine.search(query, limit)
            
            self.logger.info(f"文本搜索完成: 查询='{query}', 结果数={len(results)}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"文本搜索失败: {e}")
            return []
    
    def search_image(self, image_path: str, limit: int = 50, 
                    threshold: float = 0.7) -> List[Dict[str, Any]]:
        """图像搜索"""
        try:
            self.search_stats['total_searches'] += 1
            self.search_stats['image_searches'] += 1
            
            results = self.image_engine.search_similar_images(image_path, limit, threshold)
            
            self.logger.info(f"图像搜索完成: 查询图像='{image_path}', 结果数={len(results)}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"图像搜索失败: {e}")
            return []
    
    def add_file_to_index(self, file_record: SharedFile):
        """添加文件到搜索索引"""
        try:
            # 添加到文本索引
            self.text_engine.add_file_to_index(file_record)
            
            # 如果是图像，添加到图像索引
            if file_record.is_image:
                full_path = file_record.get_full_path()
                if os.path.exists(full_path):
                    self.image_engine.add_image_to_index(file_record, full_path)
            
        except Exception as e:
            self.logger.error(f"添加文件到搜索索引失败: {e}")
    
    def remove_file_from_index(self, file_id: int):
        """从搜索索引中移除文件"""
        try:
            self.text_engine.remove_file_from_index(file_id)
            self.image_engine.remove_image_from_index(file_id)
            
        except Exception as e:
            self.logger.error(f"从搜索索引移除文件失败: {e}")
    
    def rebuild_index(self):
        """重建搜索索引"""
        try:
            self.logger.info("开始重建搜索索引")
            
            # 重建文本索引
            self.text_engine.rebuild_index(self.db_manager)
            
            # 重建图像索引
            self.image_engine.image_index = {}
            
            with self.db_manager.get_session() as session:
                image_files = session.query(SharedFile).filter_by(is_image=True).all()
                
                for file_record in image_files:
                    full_path = file_record.get_full_path()
                    if os.path.exists(full_path):
                        self.image_engine.add_image_to_index(file_record, full_path)
            
            self.image_engine.save_index()
            
            self.logger.info("搜索索引重建完成")
            
        except Exception as e:
            self.logger.error(f"重建搜索索引失败: {e}")
    
    def get_search_statistics(self) -> Dict[str, Any]:
        """获取搜索统计信息"""
        return {
            'search_stats': self.search_stats,
            'text_index_size': len(self.text_engine.file_index),
            'image_index_size': len(self.image_engine.image_index)
        }

# 为了兼容性，创建别名
SearchService = SimpleSearchService
