/**
 * 滚动字幕管理模块
 * 简单实用的滚动字幕功能，适合大学作业水平
 */

class MarqueeManager {
    constructor() {
        this.container = null;
        this.textElement = null;
        this.closeButton = null;
        this.isVisible = true;
        this.currentMessage = '';
        this.refreshInterval = null;
        
        this.init();
    }
    
    /**
     * 初始化
     */
    init() {
        this.bindElements();
        this.bindEvents();
        this.loadMarqueeMessage();
        this.startAutoRefresh();
    }
    
    /**
     * 绑定DOM元素
     */
    bindElements() {
        // 使用基础的DOM查询方法，避免依赖Utils
        this.container = document.getElementById('marquee-container');
        this.textElement = document.getElementById('marquee-text');
        this.closeButton = document.getElementById('marquee-close');
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        if (this.closeButton) {
            this.closeButton.addEventListener('click', () => {
                this.hide();
            });
        }
        
        // 鼠标悬停暂停滚动
        if (this.container) {
            this.container.addEventListener('mouseenter', () => {
                this.pauseAnimation();
            });
            
            this.container.addEventListener('mouseleave', () => {
                this.resumeAnimation();
            });
        }
    }
    
    /**
     * 显示滚动字幕
     */
    show(message = '', theme = '') {
        if (!this.container || !this.textElement) return;
        
        if (message) {
            this.textElement.textContent = message;
            this.currentMessage = message;
        }
        
        // 设置主题
        this.setTheme(theme);
        
        this.container.classList.remove('hidden', 'fade-out');
        this.isVisible = true;
        
        // 重新开始动画
        this.restartAnimation();
    }
    
    /**
     * 隐藏滚动字幕
     */
    hide() {
        if (!this.container) return;
        
        this.container.classList.add('fade-out');
        setTimeout(() => {
            this.container.classList.add('hidden');
            this.isVisible = false;
        }, 300);
    }
    
    /**
     * 设置字幕内容
     */
    setMessage(message, theme = '') {
        if (!this.textElement) return;
        
        this.textElement.textContent = message;
        this.currentMessage = message;
        this.setTheme(theme);
        this.restartAnimation();
    }
    
    /**
     * 设置主题样式
     */
    setTheme(theme) {
        if (!this.container) return;
        
        // 清除所有主题类
        this.container.classList.remove('theme-info', 'theme-success', 'theme-warning', 'theme-error');
        
        // 添加新主题类
        if (theme && ['info', 'success', 'warning', 'error'].includes(theme)) {
            this.container.classList.add(`theme-${theme}`);
        }
    }
    
    /**
     * 暂停动画
     */
    pauseAnimation() {
        if (this.container) {
            this.container.classList.add('marquee-paused');
        }
    }
    
    /**
     * 恢复动画
     */
    resumeAnimation() {
        if (this.container) {
            this.container.classList.remove('marquee-paused');
        }
    }
    
    /**
     * 重新开始动画
     */
    restartAnimation() {
        if (!this.textElement) return;
        
        // 移除动画类
        this.textElement.style.animation = 'none';
        
        // 强制重排
        this.textElement.offsetHeight;
        
        // 重新添加动画
        this.textElement.style.animation = '';
    }
    
    /**
     * 从服务器加载字幕内容
     */
    async loadMarqueeMessage() {
        try {
            // 检查必要的依赖是否存在
            if (typeof api === 'undefined' || typeof Utils === 'undefined') {
                console.warn('Dependencies not available for marquee, using default message');
                return;
            }

            const authData = Utils.storage.get('fileShareAuth');
            const token = authData ? authData.token : '';
            
            const response = await fetch(`${api.getBaseURL()}/api/system/marquee`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data && data.data.message) {
                    this.setMessage(data.data.message, data.data.theme || '');
                    
                    // 如果设置了隐藏，则隐藏字幕
                    if (data.data.hidden) {
                        this.hide();
                    } else {
                        this.show();
                    }
                }
            }
        } catch (error) {
            // 静默失败，使用默认消息
            if (typeof CONFIG !== 'undefined' && CONFIG.log) {
                CONFIG.log('warn', 'Failed to load marquee message:', error);
            } else {
                console.warn('Failed to load marquee message:', error);
            }
        }
    }
    
    /**
     * 开始自动刷新
     */
    startAutoRefresh() {
        // 每5分钟检查一次是否有新的字幕内容
        this.refreshInterval = setInterval(() => {
            this.loadMarqueeMessage();
        }, 5 * 60 * 1000); // 5分钟
    }
    
    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    /**
     * 显示系统通知字幕
     */
    showNotification(message, theme = 'info', duration = 10000) {
        this.setMessage(message, theme);
        this.show();
        
        // 自动隐藏（如果设置了持续时间）
        if (duration > 0) {
            setTimeout(() => {
                this.loadMarqueeMessage(); // 恢复到原始消息
            }, duration);
        }
    }
    
    /**
     * 销毁
     */
    destroy() {
        this.stopAutoRefresh();
        
        // 清理事件监听器（由于使用addEventListener，需要记录函数引用才能移除）
        // 这里我们简化处理，依靠页面卸载来清理
        if (this.container) {
            this.container.style.display = 'none';
        }
    }
}

// 全局实例
let marqueeManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    marqueeManager = new MarqueeManager();
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (marqueeManager) {
        marqueeManager.destroy();
    }
}); 