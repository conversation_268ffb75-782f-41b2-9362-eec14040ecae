#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载记录功能数据库升级脚本
"""

import pymysql
import json
from datetime import datetime

def upgrade_download_tables():
    """升级下载记录相关数据库表"""
    try:
        # 连接数据库
        conn = pymysql.connect(
            host='localhost',
            user='root', 
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        cursor = conn.cursor()
        
        print("正在升级下载记录数据库表...")
        
        # 1. 创建下载批次表
        print("1. 创建 download_batches 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS download_batches (
                id INT AUTO_INCREMENT PRIMARY KEY,
                batch_id VARCHAR(50) UNIQUE NOT NULL COMMENT '批次唯一标识',
                user_id INT COMMENT '用户ID',
                session_id VARCHAR(100) COMMENT '会话ID',
                batch_type VARCHAR(20) NOT NULL COMMENT '批次类型: single/batch/folder',
                target_type VARCHAR(20) NOT NULL COMMENT '目标类型: file/folder',
                target_id INT COMMENT '目标ID（文件或文件夹）',
                total_files INT DEFAULT 0 COMMENT '文件总数',
                total_size BIGINT DEFAULT 0 COMMENT '总大小（字节）',
                compressed_size BIGINT DEFAULT 0 COMMENT '压缩后大小（字节）',
                download_name VARCHAR(255) NOT NULL COMMENT '下载包名称',
                is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密',
                password_required BOOLEAN DEFAULT FALSE COMMENT '是否需要密码',
                ip_address VARCHAR(45) COMMENT '请求IP地址',
                user_agent TEXT COMMENT '用户代理',
                referer TEXT COMMENT '来源页面',
                download_source VARCHAR(50) DEFAULT 'web' COMMENT '下载来源: web/api/mobile',
                status VARCHAR(20) DEFAULT 'preparing' COMMENT '状态: preparing/ready/downloading/completed/failed/expired',
                error_message TEXT COMMENT '错误信息',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                started_at DATETIME COMMENT '开始下载时间',
                completed_at DATETIME COMMENT '完成时间',
                expires_at DATETIME COMMENT '过期时间',
                extra_data JSON COMMENT '扩展元数据',
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_batch_id (batch_id),
                INDEX idx_user_batch (user_id, created_at),
                INDEX idx_status_created (status, created_at),
                INDEX idx_target (target_type, target_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ download_batches 表创建成功")
        
        # 2. 创建用户下载活动统计表
        print("2. 创建 user_download_activities 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_download_activities (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT COMMENT '用户ID，NULL表示匿名用户',
                activity_date DATE NOT NULL COMMENT '活动日期',
                total_downloads INT DEFAULT 0 COMMENT '总下载次数',
                single_downloads INT DEFAULT 0 COMMENT '单文件下载次数',
                batch_downloads INT DEFAULT 0 COMMENT '批量下载次数',
                folder_downloads INT DEFAULT 0 COMMENT '文件夹下载次数',
                total_size BIGINT DEFAULT 0 COMMENT '总下载大小（字节）',
                avg_file_size BIGINT DEFAULT 0 COMMENT '平均文件大小（字节）',
                max_file_size BIGINT DEFAULT 0 COMMENT '最大文件大小（字节）',
                file_types JSON COMMENT '文件类型统计',
                encrypted_downloads INT DEFAULT 0 COMMENT '加密下载次数',
                password_requests INT DEFAULT 0 COMMENT '密码申请次数',
                first_download_time DATETIME COMMENT '首次下载时间',
                last_download_time DATETIME COMMENT '最后下载时间',
                peak_hour INT COMMENT '下载高峰时段（0-23）',
                unique_sessions INT DEFAULT 0 COMMENT '独立会话数',
                unique_ips INT DEFAULT 0 COMMENT '独立IP数',
                most_used_source VARCHAR(50) COMMENT '最常用来源',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                UNIQUE KEY unique_user_date (user_id, activity_date),
                INDEX idx_activity_date (activity_date),
                INDEX idx_user_activity (user_id, activity_date)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ user_download_activities 表创建成功")
        
        # 3. 创建用户下载汇总统计表
        print("3. 创建 user_download_summaries 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_download_summaries (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT COMMENT '用户ID，NULL表示匿名用户',
                total_downloads INT DEFAULT 0 COMMENT '总下载次数',
                total_size BIGINT DEFAULT 0 COMMENT '总下载大小（字节）',
                total_files INT DEFAULT 0 COMMENT '总文件数',
                single_downloads INT DEFAULT 0 COMMENT '单文件下载次数',
                batch_downloads INT DEFAULT 0 COMMENT '批量下载次数',
                folder_downloads INT DEFAULT 0 COMMENT '文件夹下载次数',
                encrypted_downloads INT DEFAULT 0 COMMENT '加密下载次数',
                password_requests INT DEFAULT 0 COMMENT '密码申请次数',
                successful_requests INT DEFAULT 0 COMMENT '成功申请次数',
                first_download DATETIME COMMENT '首次下载时间',
                last_download DATETIME COMMENT '最后下载时间',
                most_active_day DATE COMMENT '最活跃日期',
                most_active_hour INT COMMENT '最活跃时段',
                favorite_file_types JSON COMMENT '偏好文件类型统计',
                preferred_download_source VARCHAR(50) COMMENT '偏好下载来源',
                avg_batch_size INT DEFAULT 0 COMMENT '平均批次大小',
                active_days INT DEFAULT 0 COMMENT '活跃天数',
                unique_sessions INT DEFAULT 0 COMMENT '独立会话数',
                unique_ips INT DEFAULT 0 COMMENT '独立IP数',
                last_calculated DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '最后计算时间',
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                UNIQUE KEY unique_user_summary (user_id),
                INDEX idx_last_calculated (last_calculated)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """)
        print("✅ user_download_summaries 表创建成功")
        
        # 4. 检查并升级现有的 download_records 表
        print("4. 升级 download_records 表...")
        
        # 检查是否需要添加新字段
        cursor.execute("SHOW COLUMNS FROM download_records")
        existing_columns = {row[0] for row in cursor.fetchall()}
        
        new_columns = [
            ('batch_id', 'INT', 'ADD COLUMN batch_id INT COMMENT \'所属批次ID\' AFTER user_id'),
            ('session_id', 'VARCHAR(100)', 'ADD COLUMN session_id VARCHAR(100) COMMENT \'会话ID\' AFTER batch_id'),
            ('ip_address', 'VARCHAR(45)', 'ADD COLUMN ip_address VARCHAR(45) COMMENT \'请求IP地址\' AFTER session_id'),
            ('user_agent', 'TEXT', 'ADD COLUMN user_agent TEXT COMMENT \'用户代理\' AFTER ip_address'),
            ('download_source', 'VARCHAR(50)', 'ADD COLUMN download_source VARCHAR(50) DEFAULT \'web\' COMMENT \'下载来源: web/api/mobile\' AFTER user_agent')
        ]
        
        for col_name, col_type, alter_sql in new_columns:
            if col_name not in existing_columns:
                cursor.execute(f"ALTER TABLE download_records {alter_sql}")
                print(f"   ✅ 添加字段: {col_name}")
            else:
                print(f"   ⏭️  字段已存在: {col_name}")
        
        # 添加外键约束（如果不存在）
        try:
            cursor.execute("""
                ALTER TABLE download_records 
                ADD CONSTRAINT fk_download_batch 
                FOREIGN KEY (batch_id) REFERENCES download_batches(id) ON DELETE SET NULL
            """)
            print("   ✅ 添加批次外键约束")
        except pymysql.Error as e:
            if "Duplicate key" in str(e) or "already exists" in str(e):
                print("   ⏭️  批次外键约束已存在")
            else:
                print(f"   ⚠️  添加外键约束失败: {e}")
        
        # 5. 添加必要的索引
        print("5. 添加索引...")
        
        indexes = [
            ('download_records', 'idx_batch_session', 'batch_id, session_id'),
            ('download_records', 'idx_source_created', 'download_source, created_at'),
            ('download_records', 'idx_ip_created', 'ip_address, created_at'),
        ]
        
        for table, index_name, columns in indexes:
            try:
                cursor.execute(f"CREATE INDEX {index_name} ON {table} ({columns})")
                print(f"   ✅ 创建索引: {index_name}")
            except pymysql.Error as e:
                if "Duplicate key" in str(e) or "already exists" in str(e):
                    print(f"   ⏭️  索引已存在: {index_name}")
                else:
                    print(f"   ⚠️  创建索引失败: {index_name} - {e}")
        
        # 6. 提交更改
        conn.commit()
        
        # 7. 显示表结构
        print("\n📋 更新后的表结构:")
        print("-" * 80)
        
        tables = ['download_records', 'download_batches', 'user_download_activities', 'user_download_summaries']
        for table in tables:
            try:
                cursor.execute(f'DESCRIBE {table}')
                result = cursor.fetchall()
                print(f"\n{table.upper()}:")
                for row in result:
                    field, type_info, null, key, default, extra = row
                    print(f"  {field:<20} | {type_info:<25} | {null:<5} | {key:<4}")
            except pymysql.Error:
                print(f"  {table} - 表不存在或无法访问")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 下载记录数据库升级完成!")
        return True
        
    except Exception as e:
        print(f"❌ 数据库升级失败: {e}")
        return False

def downgrade_download_tables():
    """回滚下载记录相关数据库更改"""
    try:
        conn = pymysql.connect(
            host='localhost',
            user='root', 
            password='123456',
            database='file_share_system',
            charset='utf8mb4'
        )
        cursor = conn.cursor()
        
        print("正在回滚下载记录数据库更改...")
        
        # 删除新增的表
        tables_to_drop = [
            'user_download_summaries',
            'user_download_activities', 
            'download_batches'
        ]
        
        for table in tables_to_drop:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
                print(f"✅ 删除表: {table}")
            except Exception as e:
                print(f"❌ 删除表 {table} 失败: {e}")
        
        # 删除新增的字段
        columns_to_drop = [
            'download_source',
            'user_agent', 
            'ip_address',
            'session_id',
            'batch_id'
        ]
        
        for column in columns_to_drop:
            try:
                cursor.execute(f"ALTER TABLE download_records DROP COLUMN {column}")
                print(f"✅ 删除字段: download_records.{column}")
            except Exception as e:
                print(f"❌ 删除字段 {column} 失败: {e}")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print("🎉 数据库回滚完成!")
        return True
        
    except Exception as e:
        print(f"❌ 数据库回滚失败: {e}")
        return False

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "downgrade":
        downgrade_download_tables()
    else:
        upgrade_download_tables() 