// 调试下载记录功能的脚本
// 在浏览器控制台中运行这些函数来测试

window.debugDownloads = {
    // 测试登录状态
    checkAuth() {
        const authData = localStorage.getItem('fileShareAuth');
        if (authData) {
            try {
                const auth = JSON.parse(authData);
                console.log('认证信息:', auth);
                return auth;
            } catch (e) {
                console.error('认证数据解析失败:', e);
                return null;
            }
        } else {
            console.log('未找到认证信息');
            return null;
        }
    },

    // 测试API调用
    async testDownloadRecordsAPI() {
        try {
            console.log('测试下载记录API...');
            const auth = this.checkAuth();
            if (!auth || !auth.token) {
                console.error('未登录或token无效');
                return;
            }

            const response = await fetch('http://localhost:8086/api/download/records', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${auth.token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('API响应状态:', response.status);
            
            if (response.ok) {
                const data = await response.json();
                console.log('API响应数据:', data);
                return data;
            } else {
                const errorText = await response.text();
                console.error('API错误:', errorText);
            }
        } catch (error) {
            console.error('API调用失败:', error);
        }
    },

    // 测试DownloadAPI类
    async testDownloadAPIClass() {
        try {
            console.log('测试DownloadAPI类...');
            if (typeof DownloadAPI === 'undefined') {
                console.error('DownloadAPI未定义');
                return;
            }

            const records = await DownloadAPI.getDownloadRecords();
            console.log('DownloadAPI返回:', records);
            return records;
        } catch (error) {
            console.error('DownloadAPI调用失败:', error);
        }
    },

    // 测试文件管理器的下载记录方法
    async testFileManagerMethod() {
        try {
            console.log('测试FileManager下载记录方法...');
            if (typeof fileManager === 'undefined') {
                console.error('fileManager未定义');
                return;
            }

            if (typeof fileManager.getDownloadRecords !== 'function') {
                console.error('fileManager.getDownloadRecords方法不存在');
                return;
            }

            const records = await fileManager.getDownloadRecords();
            console.log('FileManager返回:', records);
            return records;
        } catch (error) {
            console.error('FileManager方法调用失败:', error);
        }
    },

    // 测试切换到下载记录视图
    testSwitchView() {
        try {
            console.log('测试切换到下载记录视图...');
            if (typeof fileManager === 'undefined') {
                console.error('fileManager未定义');
                return;
            }

            if (typeof fileManager.switchView !== 'function') {
                console.error('fileManager.switchView方法不存在');
                return;
            }

            fileManager.switchView('downloads');
            console.log('切换视图完成');
        } catch (error) {
            console.error('切换视图失败:', error);
        }
    },

    // 测试菜单点击事件
    testMenuClick() {
        try {
            console.log('测试菜单点击事件...');
            const menuLink = document.querySelector('a[data-view="downloads"]');
            if (!menuLink) {
                console.error('未找到下载记录菜单链接');
                return;
            }

            console.log('找到菜单链接:', menuLink);
            menuLink.click();
            console.log('菜单点击完成');
        } catch (error) {
            console.error('菜单点击失败:', error);
        }
    },

    // 检查下载记录视图容器
    checkDownloadRecordsView() {
        const container = document.getElementById('download-records-view');
        if (container) {
            console.log('下载记录视图容器存在:', container);
            console.log('容器类名:', container.className);
            console.log('容器内容:', container.innerHTML);
            return container;
        } else {
            console.error('下载记录视图容器不存在');
            return null;
        }
    },

    // 运行所有测试
    async runAllTests() {
        console.log('=== 开始调试下载记录功能 ===');
        
        console.log('\n1. 检查认证状态:');
        this.checkAuth();
        
        console.log('\n2. 检查下载记录视图容器:');
        this.checkDownloadRecordsView();
        
        console.log('\n3. 测试API直接调用:');
        await this.testDownloadRecordsAPI();
        
        console.log('\n4. 测试DownloadAPI类:');
        await this.testDownloadAPIClass();
        
        console.log('\n5. 测试FileManager方法:');
        await this.testFileManagerMethod();
        
        console.log('\n6. 测试切换视图:');
        this.testSwitchView();
        
        console.log('\n=== 调试完成 ===');
    }
};

console.log('调试工具已加载，使用 debugDownloads.runAllTests() 运行所有测试');
