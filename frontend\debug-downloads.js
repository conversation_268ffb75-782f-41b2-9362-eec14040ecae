// 调试下载记录功能的脚本
// 在浏览器控制台中运行这些函数来测试

window.debugDownloads = {
    // 测试登录状态
    checkAuth() {
        const authData = localStorage.getItem('fileShareAuth');
        if (authData) {
            try {
                const auth = JSON.parse(authData);
                console.log('认证信息:', auth);
                return auth;
            } catch (e) {
                console.error('认证数据解析失败:', e);
                return null;
            }
        } else {
            console.log('未找到认证信息');
            return null;
        }
    },

    // 测试API调用
    async testDownloadRecordsAPI() {
        try {
            console.log('测试下载记录API...');
            const auth = this.checkAuth();
            if (!auth || !auth.token) {
                console.error('未登录或token无效');
                return;
            }

            // 获取当前服务器地址
            const serverUrl = auth.serverUrl || 'http://localhost:8086';
            const apiUrl = `${serverUrl}/api/download/records`;

            console.log('API地址:', apiUrl);
            console.log('使用token:', auth.token.substring(0, 20) + '...');

            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${auth.token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('API响应状态:', response.status);
            console.log('API响应头:', Object.fromEntries(response.headers.entries()));

            if (response.ok) {
                const data = await response.json();
                console.log('API响应数据:', data);
                return data;
            } else {
                const errorText = await response.text();
                console.error('API错误:', errorText);
                console.error('响应状态:', response.status, response.statusText);
            }
        } catch (error) {
            console.error('API调用失败:', error);
        }
    },

    // 测试DownloadAPI类
    async testDownloadAPIClass() {
        try {
            console.log('测试DownloadAPI类...');
            if (typeof DownloadAPI === 'undefined') {
                console.error('DownloadAPI未定义');
                return;
            }

            const records = await DownloadAPI.getDownloadRecords();
            console.log('DownloadAPI返回:', records);
            return records;
        } catch (error) {
            console.error('DownloadAPI调用失败:', error);
        }
    },

    // 测试文件管理器的下载记录方法
    async testFileManagerMethod() {
        try {
            console.log('测试FileManager下载记录方法...');
            if (typeof fileManager === 'undefined') {
                console.error('fileManager未定义');
                return;
            }

            if (typeof fileManager.getDownloadRecords !== 'function') {
                console.error('fileManager.getDownloadRecords方法不存在');
                return;
            }

            const records = await fileManager.getDownloadRecords();
            console.log('FileManager返回:', records);
            return records;
        } catch (error) {
            console.error('FileManager方法调用失败:', error);
        }
    },

    // 测试切换到下载记录视图
    testSwitchView() {
        try {
            console.log('测试切换到下载记录视图...');
            if (typeof fileManager === 'undefined') {
                console.error('fileManager未定义');
                return;
            }

            if (typeof fileManager.switchView !== 'function') {
                console.error('fileManager.switchView方法不存在');
                return;
            }

            fileManager.switchView('downloads');
            console.log('切换视图完成');
        } catch (error) {
            console.error('切换视图失败:', error);
        }
    },

    // 测试菜单点击事件
    testMenuClick() {
        try {
            console.log('测试菜单点击事件...');
            const menuLink = document.querySelector('a[data-view="downloads"]');
            if (!menuLink) {
                console.error('未找到下载记录菜单链接');
                return;
            }

            console.log('找到菜单链接:', menuLink);
            menuLink.click();
            console.log('菜单点击完成');
        } catch (error) {
            console.error('菜单点击失败:', error);
        }
    },

    // 检查下载记录视图容器
    checkDownloadRecordsView() {
        const container = document.getElementById('download-records-view');
        if (container) {
            console.log('下载记录视图容器存在:', container);
            console.log('容器类名:', container.className);
            console.log('容器内容:', container.innerHTML);
            return container;
        } else {
            console.error('下载记录视图容器不存在');
            return null;
        }
    },

    // 清除认证信息并重新登录
    clearAuthAndRelogin() {
        console.log('清除认证信息...');
        localStorage.removeItem('fileShareAuth');
        localStorage.removeItem('token'); // 清除可能存在的旧token
        sessionStorage.clear();

        console.log('认证信息已清除，请重新登录');
        alert('认证信息已清除，页面将跳转到登录页面');
        window.location.href = 'login.html';
    },

    // 修复认证问题
    async fixAuthIssues() {
        console.log('🔧 开始修复认证问题...');

        // 1. 检查认证数据
        const auth = this.checkAuth();
        if (!auth) {
            console.log('❌ 未找到认证数据，需要重新登录');
            this.clearAuthAndRelogin();
            return;
        }

        // 2. 测试token有效性
        try {
            const serverUrl = auth.serverUrl || 'http://localhost:8086';
            const response = await fetch(`${serverUrl}/api/auth/verify`, {
                headers: {
                    'Authorization': `Bearer ${auth.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (data.valid) {
                    console.log('✅ Token有效，认证正常');

                    // 3. 测试下载记录API
                    const downloadResult = await this.testDownloadRecordsAPI();
                    if (downloadResult) {
                        console.log('✅ 下载记录API正常');
                        alert('认证问题已修复！请刷新页面重试。');
                        window.location.reload();
                    } else {
                        console.log('❌ 下载记录API仍有问题');
                    }
                } else {
                    console.log('❌ Token无效:', data.error);
                    this.clearAuthAndRelogin();
                }
            } else {
                console.log('❌ Token验证失败:', response.status);
                this.clearAuthAndRelogin();
            }
        } catch (error) {
            console.error('❌ 认证修复失败:', error);
            this.clearAuthAndRelogin();
        }
    },

    // 运行所有测试
    async runAllTests() {
        console.log('=== 开始调试下载记录功能 ===');

        console.log('\n1. 检查认证状态:');
        this.checkAuth();

        console.log('\n2. 检查下载记录视图容器:');
        this.checkDownloadRecordsView();

        console.log('\n3. 测试API直接调用:');
        await this.testDownloadRecordsAPI();

        console.log('\n4. 测试DownloadAPI类:');
        await this.testDownloadAPIClass();

        console.log('\n5. 测试FileManager方法:');
        await this.testFileManagerMethod();

        console.log('\n6. 测试切换视图:');
        this.testSwitchView();

        console.log('\n=== 调试完成 ===');
    }
};

console.log('🔧 调试工具已加载！');
console.log('使用以下命令进行调试:');
console.log('- debugDownloads.checkAuth() - 检查认证状态');
console.log('- debugDownloads.testDownloadRecordsAPI() - 测试下载记录API');
console.log('- debugDownloads.fixAuthIssues() - 修复认证问题');
console.log('- debugDownloads.runAllTests() - 运行所有测试');
