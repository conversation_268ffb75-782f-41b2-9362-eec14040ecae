#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能依赖检查脚本
检查项目所需的 Python 依赖包是否正确安装
"""

import sys
import subprocess
import importlib
import pkg_resources
from typing import List, Tuple, Dict
import re

class DependencyChecker:
    def __init__(self, requirements_file: str = "backend/requirements.txt"):
        self.requirements_file = requirements_file
        self.missing_packages = []
        self.outdated_packages = []
        self.installed_packages = []
        
    def parse_requirements(self) -> List[Tuple[str, str]]:
        """解析 requirements.txt 文件"""
        packages = []
        try:
            # 尝试多种编码方式
            encodings = ['utf-8', 'gbk', 'cp1252', 'latin1']
            content = None

            for encoding in encodings:
                try:
                    with open(self.requirements_file, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue

            if content is None:
                print(f"Error: Cannot decode {self.requirements_file} with any encoding")
                sys.exit(1)

            for line in content.splitlines():
                line = line.strip()
                if line and not line.startswith('#'):
                    # 解析包名和版本
                    match = re.match(r'^([a-zA-Z0-9_-]+)(?:==([0-9.]+))?', line)
                    if match:
                        package_name = match.group(1)
                        version = match.group(2)
                        packages.append((package_name, version))
        except FileNotFoundError:
            print(f"Error: Cannot find dependency file {self.requirements_file}")
            sys.exit(1)
        return packages
    
    def check_package(self, package_name: str, required_version: str = None) -> Tuple[bool, str, str]:
        """
        检查单个包的安装状态
        返回: (是否已安装, 当前版本, 状态信息)
        """
        try:
            # 特殊处理一些包名映射
            import_name = package_name.lower().replace('-', '_')
            if package_name.lower() == 'pillow':
                import_name = 'PIL'
            elif package_name.lower() == 'opencv-python':
                import_name = 'cv2'
            elif package_name.lower() == 'pyyaml':
                import_name = 'yaml'
            
            # 尝试导入包
            module = importlib.import_module(import_name)
            
            # 获取版本信息
            current_version = None
            if hasattr(module, '__version__'):
                current_version = module.__version__
            else:
                # 尝试通过 pkg_resources 获取版本
                try:
                    current_version = pkg_resources.get_distribution(package_name).version
                except:
                    current_version = "unknown"
            
            # 检查版本匹配
            if required_version and current_version != "unknown":
                if current_version != required_version:
                    return False, current_version, f"VERSION MISMATCH (current: {current_version}, required: {required_version})"
            
            return True, current_version, f"OK - Installed (version: {current_version})"

        except ImportError:
            return False, None, "MISSING - Not installed"
        except Exception as e:
            return False, None, f"ERROR - Check failed: {str(e)}"
    
    def check_all_dependencies(self) -> bool:
        """检查所有依赖"""
        print("Checking project dependencies...")
        print("=" * 60)

        packages = self.parse_requirements()
        all_ok = True

        for package_name, required_version in packages:
            installed, current_version, status = self.check_package(package_name, required_version)

            print(f"  {package_name:20} : {status}")

            if current_version is None:  # 包未安装
                self.missing_packages.append((package_name, required_version))
                all_ok = False
            elif not installed:  # 包已安装但版本不匹配
                self.outdated_packages.append((package_name, current_version, required_version))
                all_ok = False
            else:  # 包已正确安装
                self.installed_packages.append((package_name, current_version))

        print("=" * 60)
        return all_ok
    
    def print_summary(self):
        """打印检查摘要"""
        print(f"\nDependency Check Summary:")
        print(f"  OK: {len(self.installed_packages)} packages")
        print(f"  Missing: {len(self.missing_packages)} packages")
        print(f"  Version mismatch: {len(self.outdated_packages)} packages")

        if self.missing_packages:
            print(f"\nMissing packages:")
            for package, version in self.missing_packages:
                version_str = f"=={version}" if version else ""
                print(f"  - {package}{version_str}")

        if self.outdated_packages:
            print(f"\nVersion mismatch packages:")
            for package, current, required in self.outdated_packages:
                print(f"  - {package}: {current} -> {required}")
    
    def generate_install_command(self) -> str:
        """生成安装命令"""
        if not self.missing_packages and not self.outdated_packages:
            return ""
        
        packages_to_install = []
        for package, version in self.missing_packages:
            if version:
                packages_to_install.append(f"{package}=={version}")
            else:
                packages_to_install.append(package)
        
        for package, current, required in self.outdated_packages:
            packages_to_install.append(f"{package}=={required}")
        
        return f"pip install {' '.join(packages_to_install)}"

def main():
    """主函数"""
    print("File Share System - Dependency Checker")
    print("=" * 60)

    checker = DependencyChecker()

    # 检查所有依赖
    all_ok = checker.check_all_dependencies()

    # 打印摘要
    checker.print_summary()

    if all_ok:
        print(f"\nAll dependencies are correctly installed!")
        sys.exit(0)
    else:
        print(f"\nDependency issues found, need to install or update packages")

        # 生成安装命令
        install_cmd = checker.generate_install_command()
        if install_cmd:
            print(f"\nSuggested install command:")
            print(f"  {install_cmd}")

        sys.exit(1)

if __name__ == "__main__":
    main()
