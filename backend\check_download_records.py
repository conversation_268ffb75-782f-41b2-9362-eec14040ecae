#!/usr/bin/env python3
"""
检查下载记录数据库脚本
"""

import os
import sys
import sqlite3
from datetime import datetime

def check_database():
    """检查数据库中的下载记录"""
    db_path = os.path.join('data', 'file_share_system.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"📊 检查数据库: {db_path}")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        
        print("📋 数据库表列表:")
        for table in tables:
            print(f"  - {table[0]}")
        
        print("\n" + "=" * 60)
        
        # 检查下载记录表结构
        if ('download_records',) in tables:
            print("📥 下载记录表 (download_records) 结构:")
            cursor.execute("PRAGMA table_info(download_records)")
            columns = cursor.fetchall()
            
            print("  列信息:")
            for col in columns:
                cid, name, type_name, notnull, default_value, pk = col
                print(f"    {name} ({type_name}) {'NOT NULL' if notnull else 'NULL'} {'PK' if pk else ''}")
            
            # 检查记录数
            cursor.execute("SELECT COUNT(*) FROM download_records")
            count = cursor.fetchone()[0]
            print(f"\n  总记录数: {count}")
            
            if count > 0:
                # 显示最近的记录
                cursor.execute("""
                    SELECT id, file_id, folder_id, user_id, download_type, 
                           zip_filename, file_size, is_encrypted, download_status,
                           ip_address, download_source, created_at, downloaded_at
                    FROM download_records 
                    ORDER BY created_at DESC 
                    LIMIT 5
                """)
                records = cursor.fetchall()
                
                print("\n  最近5条记录:")
                print("  " + "-" * 100)
                for record in records:
                    print(f"  ID:{record[0]} 文件:{record[1]} 用户:{record[3]} 类型:{record[4]} 状态:{record[8]} 时间:{record[11]}")
        else:
            print("❌ 下载记录表不存在")
        
        print("\n" + "=" * 60)
        
        # 检查其他相关表
        for table_name in ['download_batches', 'download_statistics', 'user_download_activities']:
            if (table_name,) in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"📊 {table_name}: {count} 条记录")
            else:
                print(f"❌ {table_name}: 表不存在")
        
        conn.close()
        
        print("\n✅ 数据库检查完成")
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")

def fix_database_schema():
    """修复数据库表结构"""
    print("\n🔧 修复数据库表结构...")
    
    db_path = os.path.join('data', 'file_share_system.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查 download_records 表是否有 batch_id 列
        cursor.execute("PRAGMA table_info(download_records)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'batch_id' not in column_names:
            print("  添加 batch_id 列...")
            cursor.execute("ALTER TABLE download_records ADD COLUMN batch_id INTEGER")
            print("  ✅ batch_id 列添加成功")
        else:
            print("  ✅ batch_id 列已存在")
        
        # 检查其他可能缺失的列
        required_columns = {
            'session_id': 'TEXT',
            'ip_address': 'TEXT', 
            'user_agent': 'TEXT',
            'download_source': 'TEXT DEFAULT "web"',
            'password': 'TEXT',
            'password_hint': 'TEXT',
            'expires_at': 'DATETIME'
        }
        
        for col_name, col_type in required_columns.items():
            if col_name not in column_names:
                print(f"  添加 {col_name} 列...")
                cursor.execute(f"ALTER TABLE download_records ADD COLUMN {col_name} {col_type}")
                print(f"  ✅ {col_name} 列添加成功")
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库表结构修复完成")
        
    except Exception as e:
        print(f"❌ 修复数据库表结构失败: {e}")

if __name__ == "__main__":
    print("🔍 文件共享系统 - 下载记录检查工具")
    print("=" * 60)
    
    check_database()
    
    # 询问是否修复
    response = input("\n是否修复数据库表结构? (y/n): ")
    if response.lower() in ['y', 'yes']:
        fix_database_schema()
        print("\n重新检查数据库...")
        check_database()
    
    print("\n" + "=" * 60)
    print("📝 说明:")
    print("1. 如果下载记录表缺少列，需要修复表结构")
    print("2. 修复后重新启动服务器")
    print("3. 下载功能应该能正常记录到数据库")
