# 🌐 局域网访问配置说明

## 概述

文件共享系统现已配置为支持局域网访问，局域网内的其他设备可以通过IP地址访问您的文件共享系统。

## 🚀 快速开始

### 1. 启动系统
```bash
# 方法1: 使用一键启动脚本
双击 "一键启动.bat"

# 方法2: 手动启动
cd backend
python main.py
```

### 2. 获取访问地址
```bash
# 运行地址获取工具
cd backend
python get_lan_address.py
```

### 3. 配置防火墙（Windows）
```bash
# 以管理员身份运行
右键点击 "配置防火墙.bat" -> "以管理员身份运行"
```

## 📍 访问地址

根据您的网络配置，可以使用以下地址访问：

### 本地访问
- **前端页面**: http://localhost:8084
- **登录页面**: http://localhost:8084/login.html
- **API地址**: http://localhost:8086

### 局域网访问
根据您的网络接口，可能的访问地址包括：
- **WLAN**: http://************:8084
- **以太网**: http://***********:8084
- **其他接口**: http://**********:8084

## 🔧 配置详情

### 服务器配置
- **前端服务器**: 绑定到 `0.0.0.0:8084`，支持所有网络接口
- **API服务器**: 绑定到 `0.0.0.0:8086`，支持所有网络接口
- **CORS配置**: 允许所有来源访问（`origins="*"`）

### 端口配置
- **前端端口**: 8084
- **API端口**: 8086

### 自动检测功能
- 前端会自动检测当前访问的IP地址
- 如果通过IP地址访问，API请求会自动使用相同的IP
- 支持动态服务器地址配置

## 🛠️ 故障排除

### 无法从其他设备访问

1. **检查防火墙设置**
   ```bash
   # 运行防火墙配置脚本
   右键 "配置防火墙.bat" -> "以管理员身份运行"
   ```

2. **检查网络连接**
   - 确保所有设备在同一局域网内
   - 检查路由器是否阻止内网通信
   - 尝试ping服务器IP地址

3. **检查杀毒软件**
   - 某些杀毒软件可能阻止网络访问
   - 将程序添加到白名单

4. **检查端口占用**
   ```bash
   # 检查端口是否被占用
   netstat -an | findstr 8084
   netstat -an | findstr 8086
   ```

### 获取正确的IP地址

1. **使用地址获取工具**
   ```bash
   cd backend
   python get_lan_address.py
   ```

2. **手动查看IP地址**
   ```bash
   # Windows
   ipconfig
   
   # Linux/Mac
   ifconfig
   hostname -I
   ```

### 常见问题

**Q: 为什么显示多个IP地址？**
A: 计算机可能有多个网络接口（WiFi、有线网络、虚拟网络等），每个接口都有自己的IP地址。

**Q: 应该使用哪个IP地址？**
A: 通常使用与其他设备在同一网段的IP地址。例如：
- 如果其他设备IP是 192.168.1.x，使用 192.168.1.x 的地址
- 如果其他设备IP是 10.0.0.x，使用 10.0.0.x 的地址

**Q: 手机可以访问吗？**
A: 可以！确保手机连接到同一WiFi网络，然后在浏览器中输入服务器的IP地址和端口。

## 🔐 安全注意事项

1. **局域网访问**: 当前配置允许局域网内所有设备访问
2. **用户认证**: 仍需要有效的用户名和密码登录
3. **防火墙**: 建议只在可信的局域网环境中使用
4. **外网访问**: 默认不允许外网访问，如需要请谨慎配置

## 📱 移动设备访问

### Android/iOS
1. 连接到同一WiFi网络
2. 打开浏览器
3. 输入服务器地址，例如：`http://*************:8084`
4. 使用正常的用户名密码登录

### 平板电脑
- 支持所有现代浏览器
- 响应式设计，自适应屏幕大小
- 支持触摸操作

## 🎯 使用建议

1. **固定IP**: 建议为服务器设置固定IP地址，避免重启后IP变化
2. **书签**: 在其他设备上添加书签，方便快速访问
3. **网络稳定**: 确保网络连接稳定，避免传输中断
4. **定期备份**: 定期备份重要数据

## 📞 技术支持

如果遇到问题，请：
1. 运行 `python get_lan_address.py` 获取详细网络信息
2. 检查服务器日志文件
3. 确认防火墙和网络设置
4. 联系技术支持并提供详细的错误信息

---

**最后更新**: 2025-06-12
**版本**: 1.0.0
