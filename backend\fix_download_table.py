#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql

def fix_download_records_table():
    """修复download_records表结构"""
    try:
        # 连接数据库
        conn = pymysql.connect(
            host='localhost',
            user='root', 
            password='123456',
            database='file_share_system'
        )
        cursor = conn.cursor()
        
        print("正在检查和修复 download_records 表...")
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'download_records'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("download_records 表已存在，检查结构...")
            
            # 检查是否有folder_id字段
            cursor.execute("SHOW COLUMNS FROM download_records LIKE 'folder_id'")
            folder_id_exists = cursor.fetchone()
            
            if not folder_id_exists:
                print("添加 folder_id 字段...")
                cursor.execute("""
                    ALTER TABLE download_records 
                    ADD COLUMN folder_id INT NULL 
                    COMMENT '文件夹ID（文件夹下载时使用）' 
                    AFTER file_id
                """)
                print("✅ folder_id 字段添加成功")
            else:
                print("✅ folder_id 字段已存在")
                
        else:
            print("download_records 表不存在，创建新表...")
            cursor.execute("""
                CREATE TABLE download_records (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    file_id INT NULL COMMENT '文件ID（单文件/批量下载时使用）',
                    folder_id INT NULL COMMENT '文件夹ID（文件夹下载时使用）',
                    user_id INT COMMENT '用户ID',
                    download_type VARCHAR(20) NOT NULL COMMENT '下载类型: single/batch/folder',
                    zip_filename VARCHAR(255) NOT NULL COMMENT '压缩文件名',
                    zip_path TEXT NOT NULL COMMENT '压缩文件路径',
                    file_size BIGINT NOT NULL COMMENT '文件大小',
                    is_encrypted BOOLEAN DEFAULT FALSE COMMENT '是否加密',
                    password VARCHAR(50) COMMENT '解压密码',
                    password_hint VARCHAR(255) COMMENT '密码提示',
                    download_status VARCHAR(20) DEFAULT 'pending' COMMENT '下载状态: pending/completed/expired',
                    expires_at DATETIME COMMENT '过期时间',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    downloaded_at DATETIME COMMENT '下载时间',
                    FOREIGN KEY (file_id) REFERENCES shared_files(id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                    INDEX idx_file_download (file_id, downloaded_at),
                    INDEX idx_folder_download (folder_id, downloaded_at),
                    INDEX idx_user_download (user_id, downloaded_at),
                    INDEX idx_download_status (download_status, created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """)
            print("✅ download_records 表创建成功")
        
        # 提交更改
        conn.commit()
        
        # 显示最终表结构
        print("\n当前 download_records 表结构:")
        print("-" * 60)
        cursor.execute('DESCRIBE download_records')
        result = cursor.fetchall()
        
        for row in result:
            field, type_info, null, key, default, extra = row
            print(f"{field:<15} | {type_info:<20} | {null:<5} | {key:<4}")
        
        cursor.close()
        conn.close()
        
        print("\n✅ 数据库表修复完成!")
        return True
        
    except Exception as e:
        print(f"❌ 数据库修复失败: {e}")
        return False

if __name__ == "__main__":
    fix_download_records_table() 