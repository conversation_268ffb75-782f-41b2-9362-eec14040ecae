#!/usr/bin/env python3
"""
检查下载记录数据库脚本
"""

import os
import sys
import sqlite3
from datetime import datetime

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.abspath(__file__))
if backend_dir not in sys.path:
    sys.path.insert(0, backend_dir)

def check_database():
    """检查数据库中的下载记录"""
    db_path = os.path.join(backend_dir, 'data', 'file_share_system.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"📊 检查数据库: {db_path}")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        
        print("📋 数据库表列表:")
        for table in tables:
            print(f"  - {table[0]}")
        
        print("\n" + "=" * 60)
        
        # 检查下载记录表
        if ('download_records',) in tables:
            print("📥 下载记录表 (download_records):")
            cursor.execute("SELECT COUNT(*) FROM download_records")
            count = cursor.fetchone()[0]
            print(f"  总记录数: {count}")
            
            if count > 0:
                # 显示最近的记录
                cursor.execute("""
                    SELECT id, file_id, folder_id, user_id, download_type, 
                           zip_filename, file_size, is_encrypted, download_status,
                           ip_address, download_source, created_at, downloaded_at
                    FROM download_records 
                    ORDER BY created_at DESC 
                    LIMIT 10
                """)
                records = cursor.fetchall()
                
                print("\n  最近10条记录:")
                print("  " + "-" * 120)
                print(f"  {'ID':<4} {'文件ID':<8} {'文件夹ID':<8} {'用户ID':<8} {'类型':<8} {'文件名':<20} {'大小':<10} {'加密':<6} {'状态':<10} {'IP':<15} {'来源':<8} {'创建时间':<20}")
                print("  " + "-" * 120)
                
                for record in records:
                    id_val, file_id, folder_id, user_id, download_type, zip_filename, file_size, is_encrypted, download_status, ip_address, download_source, created_at, downloaded_at = record
                    
                    # 格式化显示
                    file_id_str = str(file_id) if file_id else "N/A"
                    folder_id_str = str(folder_id) if folder_id else "N/A"
                    user_id_str = str(user_id) if user_id else "N/A"
                    zip_filename_short = (zip_filename[:17] + "...") if zip_filename and len(zip_filename) > 20 else (zip_filename or "N/A")
                    file_size_str = f"{file_size//1024}KB" if file_size else "N/A"
                    is_encrypted_str = "是" if is_encrypted else "否"
                    ip_str = ip_address or "N/A"
                    source_str = download_source or "N/A"
                    created_str = created_at[:19] if created_at else "N/A"
                    
                    print(f"  {id_val:<4} {file_id_str:<8} {folder_id_str:<8} {user_id_str:<8} {download_type:<8} {zip_filename_short:<20} {file_size_str:<10} {is_encrypted_str:<6} {download_status:<10} {ip_str:<15} {source_str:<8} {created_str:<20}")
        else:
            print("❌ 下载记录表不存在")
        
        print("\n" + "=" * 60)
        
        # 检查下载统计表
        if ('download_statistics',) in tables:
            print("📊 下载统计表 (download_statistics):")
            cursor.execute("SELECT COUNT(*) FROM download_statistics")
            count = cursor.fetchone()[0]
            print(f"  总记录数: {count}")
            
            if count > 0:
                cursor.execute("""
                    SELECT file_id, total_downloads, encrypted_downloads, 
                           password_requests, successful_requests,
                           first_download, last_download
                    FROM download_statistics 
                    ORDER BY total_downloads DESC 
                    LIMIT 5
                """)
                stats = cursor.fetchall()
                
                print("\n  下载最多的文件:")
                print("  " + "-" * 80)
                print(f"  {'文件ID':<8} {'总下载':<8} {'加密下载':<10} {'密码申请':<10} {'成功申请':<10} {'首次下载':<20} {'最后下载':<20}")
                print("  " + "-" * 80)
                
                for stat in stats:
                    file_id, total_downloads, encrypted_downloads, password_requests, successful_requests, first_download, last_download = stat
                    first_str = first_download[:19] if first_download else "N/A"
                    last_str = last_download[:19] if last_download else "N/A"
                    print(f"  {file_id:<8} {total_downloads:<8} {encrypted_downloads:<10} {password_requests:<10} {successful_requests:<10} {first_str:<20} {last_str:<20}")
        else:
            print("❌ 下载统计表不存在")
        
        print("\n" + "=" * 60)
        
        # 检查用户下载活动表
        if ('user_download_activities',) in tables:
            print("👤 用户下载活动表 (user_download_activities):")
            cursor.execute("SELECT COUNT(*) FROM user_download_activities")
            count = cursor.fetchone()[0]
            print(f"  总记录数: {count}")
            
            if count > 0:
                cursor.execute("""
                    SELECT user_id, activity_date, total_downloads, 
                           single_downloads, batch_downloads, folder_downloads,
                           total_size, encrypted_downloads
                    FROM user_download_activities 
                    ORDER BY activity_date DESC 
                    LIMIT 5
                """)
                activities = cursor.fetchall()
                
                print("\n  最近的用户活动:")
                print("  " + "-" * 100)
                print(f"  {'用户ID':<8} {'日期':<12} {'总下载':<8} {'单文件':<8} {'批量':<8} {'文件夹':<8} {'总大小':<10} {'加密':<8}")
                print("  " + "-" * 100)
                
                for activity in activities:
                    user_id, activity_date, total_downloads, single_downloads, batch_downloads, folder_downloads, total_size, encrypted_downloads = activity
                    size_str = f"{total_size//1024}KB" if total_size else "0KB"
                    print(f"  {user_id:<8} {activity_date:<12} {total_downloads:<8} {single_downloads:<8} {batch_downloads:<8} {folder_downloads:<8} {size_str:<10} {encrypted_downloads:<8}")
        else:
            print("❌ 用户下载活动表不存在")
        
        print("\n" + "=" * 60)
        
        # 检查用户表
        if ('users',) in tables:
            print("👥 用户表 (users):")
            cursor.execute("SELECT COUNT(*) FROM users")
            count = cursor.fetchone()[0]
            print(f"  总用户数: {count}")
            
            if count > 0:
                cursor.execute("SELECT id, username, email, is_admin, created_at FROM users ORDER BY id")
                users = cursor.fetchall()
                
                print("\n  用户列表:")
                print("  " + "-" * 80)
                print(f"  {'ID':<4} {'用户名':<15} {'邮箱':<25} {'管理员':<8} {'创建时间':<20}")
                print("  " + "-" * 80)
                
                for user in users:
                    user_id, username, email, is_admin, created_at = user
                    email_str = email or "N/A"
                    admin_str = "是" if is_admin else "否"
                    created_str = created_at[:19] if created_at else "N/A"
                    print(f"  {user_id:<4} {username:<15} {email_str:<25} {admin_str:<8} {created_str:<20}")
        
        conn.close()
        
        print("\n✅ 数据库检查完成")
        
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")

def check_download_api():
    """检查下载API是否正常"""
    print("\n🔍 检查下载API...")
    
    try:
        import requests
        
        # 检查健康状态
        response = requests.get("http://localhost:8086/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ API服务器正常运行")
            
            # 尝试获取下载记录（无认证）
            try:
                response = requests.get("http://localhost:8086/api/download/records", timeout=5)
                if response.status_code == 401:
                    print("✅ 下载记录API需要认证（正常）")
                elif response.status_code == 200:
                    data = response.json()
                    print(f"📊 下载记录API返回: {data}")
                else:
                    print(f"⚠️ 下载记录API返回状态码: {response.status_code}")
            except Exception as e:
                print(f"❌ 下载记录API检查失败: {e}")
        else:
            print(f"❌ API服务器状态异常: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ API检查失败: {e}")

if __name__ == "__main__":
    print("🔍 文件共享系统 - 下载记录检查工具")
    print("=" * 60)
    
    check_database()
    check_download_api()
    
    print("\n" + "=" * 60)
    print("📝 说明:")
    print("1. 如果下载记录表为空，说明还没有用户进行过下载")
    print("2. 下载记录需要用户登录后才能查看")
    print("3. 每次下载都会自动记录到数据库中")
    print("4. 可以通过前端界面查看个人下载历史")
