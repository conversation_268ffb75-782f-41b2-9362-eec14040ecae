@echo off
title File Share System - SQLite
chcp 65001 >nul

echo.
echo ========================================
echo   File Share System - SQLite Version
echo ========================================
echo.

:: Check Python installation
echo [1/4] 检查 Python 环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 Python，请安装 Python 3.7+
    echo.
    echo 下载地址: https://www.python.org/downloads/
    echo 或使用国内镜像: https://npm.taobao.org/mirrors/python/
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python 环境检查通过 (版本: %PYTHON_VERSION%)

:: Check if in correct directory
echo [2/4] 检查项目目录...
if not exist "backend\main.py" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)
echo ✅ 项目目录检查通过

:: Configure pip mirrors for faster download
echo [3/4] 配置依赖安装源...
set PIP_MIRRORS=-i https://pypi.tuna.tsinghua.edu.cn/simple --trusted-host pypi.tuna.tsinghua.edu.cn
echo ✅ 已配置清华大学镜像源

:: Check and install dependencies intelligently
echo [4/4] 智能检查和安装依赖...
echo.

:: Use the advanced dependency checker
python check_dependencies.py
set DEPS_CHECK_RESULT=%errorlevel%

if %DEPS_CHECK_RESULT% equ 0 (
    echo.
    echo ✅ 依赖检查完成，所有包都已正确安装
) else (
    echo.
    echo ⚠️  检测到缺失或版本不匹配的依赖包，开始安装...
    echo.

    :: Try primary mirror first
    echo 正在从清华大学镜像源安装依赖...
    pip install -r backend\requirements.txt %PIP_MIRRORS% --upgrade

    if errorlevel 1 (
        echo.
        echo ⚠️  清华源安装失败，尝试阿里云镜像源...
        pip install -r backend\requirements.txt -i https://mirrors.aliyun.com/pypi/simple --trusted-host mirrors.aliyun.com --upgrade

        if errorlevel 1 (
            echo.
            echo ⚠️  阿里云源安装失败，尝试豆瓣镜像源...
            pip install -r backend\requirements.txt -i https://pypi.douban.com/simple --trusted-host pypi.douban.com --upgrade

            if errorlevel 1 (
                echo.
                echo ❌ 所有国内镜像源都安装失败，尝试官方源...
                pip install -r backend\requirements.txt --upgrade

                if errorlevel 1 (
                    echo.
                    echo ❌ 依赖安装失败，可能的解决方案:
                    echo 1. 检查网络连接
                    echo 2. 尝试手动运行: pip install -r backend\requirements.txt
                    echo 3. 检查 Python 和 pip 版本
                    echo 4. 尝试使用虚拟环境
                    echo.
                    pause
                    exit /b 1
                )
            )
        )
    )

    echo.
    echo ✅ 依赖安装完成
)

echo.
echo ========================================
echo           系统初始化和启动
echo ========================================

:: Check if database exists
echo [5/6] 检查数据库状态...
if not exist "backend\data\file_share_system.db" (
    echo ⚠️  首次运行，正在初始化 SQLite 数据库...
    cd backend
    python init_database_sqlite.py
    cd ..

    if errorlevel 1 (
        echo ❌ 数据库初始化失败
        echo.
        echo 可能的解决方案:
        echo 1. 检查 backend\data 目录是否存在
        echo 2. 检查是否有足够的磁盘空间
        echo 3. 检查文件权限
        pause
        exit /b 1
    )

    echo ✅ 数据库初始化成功
) else (
    echo ✅ 数据库已存在，跳过初始化
)

:: Final dependency verification before starting
echo [6/6] 启动前最终检查...
python -c "import flask, sqlalchemy, PIL, requests; print('✅ 核心依赖验证通过')" 2>nul
if errorlevel 1 (
    echo ❌ 核心依赖验证失败，请重新运行脚本
    pause
    exit /b 1
)

:: Start server
echo.
echo ========================================
echo         启动文件共享系统
echo ========================================
echo.
echo 📋 默认管理员账户:
echo    用户名: admin
echo    密码: admin123
echo.
echo 🌐 系统将自动打开浏览器，如未打开请手动访问:
echo    前端界面: http://localhost:8082
echo    API接口: http://localhost:8086
echo.
echo 💡 按 Ctrl+C 停止服务器
echo.
echo 🚀 正在启动服务器...

cd backend
python main.py

:: If program exits with error, show error info
if errorlevel 1 (
    echo.
    echo ❌ 服务器启动失败，请检查错误信息
    echo.
    echo 🔧 常见解决方案:
    echo 1. 检查端口 8082 和 8086 是否被占用
    echo    - 使用命令: netstat -ano ^| findstr "8082\|8086"
    echo 2. 检查 Python 依赖是否正确安装
    echo    - 重新运行此脚本进行依赖检查
    echo 3. 检查数据库文件是否正常
    echo    - 删除 backend\data\file_share_system.db 重新初始化
    echo 4. 检查防火墙设置
    echo 5. 查看详细日志: backend\logs\ 目录
    echo.
    echo 📞 如需技术支持，请提供 backend\logs\ 目录下的日志文件
    echo.
    pause
)

cd ..

echo.
echo ========================================
echo           程序已退出
echo ========================================
