#!/usr/bin/env python3
"""
测试下载记录功能
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:8086"

def test_login():
    """测试登录并获取token"""
    print("🔐 测试用户登录...")
    
    login_data = {
        "username": "fjj",
        "password": "123456"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                token = data.get('token')
                user_info = data.get('user', {})
                print(f"✅ 登录成功: {user_info.get('username')} (ID: {user_info.get('id')})")
                return token, user_info.get('id')
            else:
                print(f"❌ 登录失败: {data.get('message')}")
        else:
            print(f"❌ 登录请求失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 登录异常: {e}")
    
    return None, None

def test_get_files(token):
    """获取文件列表"""
    print("\n📁 获取文件列表...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/api/files/folders", headers=headers)
        if response.status_code == 200:
            data = response.json()
            # 检查返回的数据格式
            if isinstance(data, list):
                folders = data
            else:
                folders = data.get('folders', [])
            print(f"✅ 找到 {len(folders)} 个文件夹")

            for folder in folders:
                print(f"  📂 {folder['name']} (ID: {folder['id']})")

                # 获取文件夹中的文件
                folder_response = requests.get(f"{BASE_URL}/api/files/folders/{folder['id']}/files", headers=headers)
                if folder_response.status_code == 200:
                    folder_data = folder_response.json()
                    files = folder_data.get('files', [])
                    print(f"    📄 包含 {len(files)} 个文件")
                    
                    for file_info in files[:3]:  # 只显示前3个文件
                        print(f"      - {file_info['filename']} (ID: {file_info['id']})")
            
            return folders
        else:
            print(f"❌ 获取文件夹失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取文件夹异常: {e}")
    
    return []

def test_download_folder(token, folder_id):
    """测试文件夹下载"""
    print(f"\n⬇️ 测试文件夹下载 (ID: {folder_id})...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/api/download/folder/{folder_id}", headers=headers)
        if response.status_code == 200:
            print("✅ 文件夹下载成功")
            print(f"  文件大小: {len(response.content)} 字节")
            return True
        else:
            print(f"❌ 文件夹下载失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"  错误信息: {error_data.get('error')}")
            except:
                print(f"  响应内容: {response.text[:200]}")
    except Exception as e:
        print(f"❌ 文件夹下载异常: {e}")
    
    return False

def test_get_download_records(token, user_id):
    """测试获取下载记录"""
    print(f"\n📊 测试获取下载记录 (用户ID: {user_id})...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/api/download/records", headers=headers)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                records = data.get('records', [])
                total = data.get('total', 0)
                print(f"✅ 获取下载记录成功: {len(records)} 条记录 (总计: {total})")
                
                if records:
                    print("  最近的下载记录:")
                    for i, record in enumerate(records[:3]):
                        print(f"    {i+1}. {record.get('filename')} ({record.get('download_type')})")
                        print(f"       大小: {record.get('file_size')} 字节")
                        print(f"       时间: {record.get('download_time')}")
                        print(f"       状态: {record.get('download_status')}")
                        print(f"       IP: {record.get('ip_address')}")
                        print()
                else:
                    print("  暂无下载记录")
                
                return records
            else:
                print(f"❌ 获取下载记录失败: {data.get('error')}")
        else:
            print(f"❌ 获取下载记录请求失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"  错误信息: {error_data.get('error')}")
            except:
                print(f"  响应内容: {response.text[:200]}")
    except Exception as e:
        print(f"❌ 获取下载记录异常: {e}")
    
    return []

def test_download_single_file(token, file_id):
    """测试单文件下载"""
    print(f"\n⬇️ 测试单文件下载 (ID: {file_id})...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.post(f"{BASE_URL}/api/download/single/{file_id}", headers=headers)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                download_info = data.get('data', {})
                print("✅ 单文件下载准备成功")
                print(f"  文件名: {download_info.get('filename')}")
                print(f"  大小: {download_info.get('file_size')} 字节")
                print(f"  加密: {'是' if download_info.get('is_encrypted') else '否'}")
                print(f"  下载URL: {download_info.get('download_url')}")
                return True
            else:
                print(f"❌ 单文件下载失败: {data.get('error')}")
        else:
            print(f"❌ 单文件下载请求失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"  错误信息: {error_data.get('error')}")
            except:
                print(f"  响应内容: {response.text[:200]}")
    except Exception as e:
        print(f"❌ 单文件下载异常: {e}")
    
    return False

def main():
    """主测试函数"""
    print("🧪 文件共享系统 - 下载记录功能测试")
    print("=" * 60)
    
    # 1. 登录获取token
    token, user_id = test_login()
    if not token:
        print("❌ 无法获取认证token，测试终止")
        return
    
    # 2. 获取文件列表
    folders = test_get_files(token)
    if not folders:
        print("❌ 无法获取文件列表，测试终止")
        return
    
    # 3. 测试下载前的记录状态
    print("\n" + "=" * 60)
    print("📊 下载前的记录状态:")
    initial_records = test_get_download_records(token, user_id)
    
    # 4. 执行下载测试
    print("\n" + "=" * 60)
    print("🔄 执行下载测试:")
    
    # 测试文件夹下载
    if folders:
        folder_id = folders[0]['id']
        test_download_folder(token, folder_id)
        
        # 等待一下让记录保存
        time.sleep(2)
    
    # 5. 测试下载后的记录状态
    print("\n" + "=" * 60)
    print("📊 下载后的记录状态:")
    final_records = test_get_download_records(token, user_id)
    
    # 6. 比较结果
    print("\n" + "=" * 60)
    print("📈 测试结果分析:")
    print(f"下载前记录数: {len(initial_records)}")
    print(f"下载后记录数: {len(final_records)}")
    
    if len(final_records) > len(initial_records):
        print("✅ 下载记录功能正常工作！")
        new_records = len(final_records) - len(initial_records)
        print(f"新增了 {new_records} 条下载记录")
    else:
        print("⚠️ 下载记录可能没有正确保存")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成")

if __name__ == "__main__":
    main()
