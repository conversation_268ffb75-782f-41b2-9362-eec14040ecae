#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

try:
    # 连接SQLite数据库
    db_path = "backend/data/file_share_system.db"
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        print("请先运行 python backend/init_database_sqlite.py 初始化数据库")
        exit(1)

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 检查download_records表结构
    print("检查 download_records 表结构:")
    print("-" * 50)
    cursor.execute("PRAGMA table_info(download_records)")
    result = cursor.fetchall()

    if result:
        print("序号 | 字段名           | 类型         | 非空 | 默认值")
        print("-" * 60)
        for row in result:
            cid, name, type_info, notnull, default_value, pk = row
            print(f"{cid:<4} | {name:<15} | {type_info:<12} | {notnull:<4} | {default_value}")
    else:
        print("download_records 表不存在")

    # 检查表是否存在
    print("\n检查所有表:")
    print("-" * 50)
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
    tables = cursor.fetchall()
    for table in tables:
        print(f"- {table[0]}")

    # 检查表记录数
    print("\n检查表记录数:")
    print("-" * 50)
    for table in tables:
        table_name = table[0]
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        print(f"{table_name:<20}: {count} 条记录")

    cursor.close()
    conn.close()
    print("\n数据库检查完成!")

except Exception as e:
    print(f"数据库连接失败: {e}")