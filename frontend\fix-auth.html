<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证问题修复工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.danger {
            background: #dc3545;
        }
        .btn.danger:hover {
            background: #c82333;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 认证问题修复工具</h1>
        <p>如果您在访问下载记录时遇到401认证错误，请使用此工具进行诊断和修复。</p>
        
        <div class="actions">
            <button class="btn" onclick="checkAuth()">检查认证状态</button>
            <button class="btn" onclick="testAPI()">测试API连接</button>
            <button class="btn" onclick="fixAuth()">自动修复</button>
            <button class="btn danger" onclick="clearAuth()">清除认证信息</button>
        </div>
        
        <div id="status"></div>
        <div id="log" class="log"></div>
        
        <div style="margin-top: 20px;">
            <a href="index.html" class="btn">返回主页</a>
            <a href="login.html" class="btn">重新登录</a>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function checkAuth() {
            log('检查认证状态...');
            
            try {
                const authData = localStorage.getItem('fileShareAuth');
                if (!authData) {
                    setStatus('未找到认证信息，需要重新登录', 'error');
                    log('❌ 未找到认证信息');
                    return false;
                }

                const auth = JSON.parse(authData);
                log('✅ 找到认证信息');
                log(`用户: ${auth.user?.username || '未知'}`);
                log(`服务器: ${auth.serverUrl || '未设置'}`);
                log(`Token: ${auth.token ? auth.token.substring(0, 20) + '...' : '无'}`);
                log(`登录时间: ${auth.loginTime ? new Date(auth.loginTime).toLocaleString() : '未知'}`);

                if (!auth.token) {
                    setStatus('Token无效，需要重新登录', 'error');
                    return false;
                }

                // 检查token是否过期
                const loginTime = auth.loginTime || 0;
                const now = Date.now();
                const maxAge = 24 * 60 * 60 * 1000; // 24小时
                
                if (now - loginTime > maxAge) {
                    setStatus('登录已过期，需要重新登录', 'warning');
                    log('⚠️ 登录已过期');
                    return false;
                }

                setStatus('认证信息正常', 'success');
                return true;
            } catch (error) {
                log(`❌ 检查认证失败: ${error.message}`);
                setStatus('认证数据损坏，需要重新登录', 'error');
                return false;
            }
        }

        async function testAPI() {
            log('测试API连接...');
            
            const authData = localStorage.getItem('fileShareAuth');
            if (!authData) {
                setStatus('请先检查认证状态', 'error');
                return;
            }

            try {
                const auth = JSON.parse(authData);
                const serverUrl = auth.serverUrl || 'http://localhost:8086';
                
                // 测试健康检查
                log('测试健康检查API...');
                const healthResponse = await fetch(`${serverUrl}/api/health`);
                if (healthResponse.ok) {
                    log('✅ 服务器连接正常');
                } else {
                    log(`❌ 服务器连接失败: ${healthResponse.status}`);
                    setStatus('服务器连接失败', 'error');
                    return;
                }

                // 测试token验证
                log('测试token验证...');
                const verifyResponse = await fetch(`${serverUrl}/api/auth/verify`, {
                    headers: {
                        'Authorization': `Bearer ${auth.token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (verifyResponse.ok) {
                    const verifyData = await verifyResponse.json();
                    if (verifyData.valid) {
                        log('✅ Token验证成功');
                        
                        // 测试下载记录API
                        log('测试下载记录API...');
                        const recordsResponse = await fetch(`${serverUrl}/api/download/records`, {
                            headers: {
                                'Authorization': `Bearer ${auth.token}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        if (recordsResponse.ok) {
                            const recordsData = await recordsResponse.json();
                            log(`✅ 下载记录API正常，获取到 ${recordsData.records?.length || 0} 条记录`);
                            setStatus('所有API测试通过！', 'success');
                        } else {
                            log(`❌ 下载记录API失败: ${recordsResponse.status}`);
                            setStatus('下载记录API访问失败', 'error');
                        }
                    } else {
                        log(`❌ Token无效: ${verifyData.error}`);
                        setStatus('Token无效，需要重新登录', 'error');
                    }
                } else {
                    log(`❌ Token验证失败: ${verifyResponse.status}`);
                    setStatus('Token验证失败', 'error');
                }

            } catch (error) {
                log(`❌ API测试失败: ${error.message}`);
                setStatus('API测试失败', 'error');
            }
        }

        async function fixAuth() {
            log('开始自动修复认证问题...');
            
            if (!checkAuth()) {
                log('认证检查失败，无法自动修复');
                return;
            }

            await testAPI();
            
            log('修复完成，建议刷新页面重试');
            setStatus('修复完成，请刷新页面重试', 'success');
        }

        function clearAuth() {
            if (confirm('确定要清除所有认证信息吗？这将需要重新登录。')) {
                log('清除认证信息...');
                localStorage.removeItem('fileShareAuth');
                localStorage.removeItem('token');
                sessionStorage.clear();
                
                log('✅ 认证信息已清除');
                setStatus('认证信息已清除，请重新登录', 'warning');
                
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
            }
        }

        // 页面加载时自动检查
        window.onload = function() {
            log('认证修复工具已加载');
            checkAuth();
        };
    </script>
</body>
</html>
