#!/usr/bin/env python3
"""
快速局域网访问测试
"""

import requests
import socket
import subprocess
import platform

def get_main_ip():
    """获取主要IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return None

def get_wifi_ip():
    """获取WiFi IP地址"""
    try:
        if platform.system() == "Windows":
            result = subprocess.run(['ipconfig'], capture_output=True, text=True, encoding='gbk')
            lines = result.stdout.split('\n')
            
            in_wlan = False
            for line in lines:
                if "无线局域网适配器 WLAN" in line or "Wireless LAN adapter Wi-Fi" in line:
                    in_wlan = True
                elif "适配器" in line and in_wlan:
                    in_wlan = False
                elif in_wlan and "IPv4" in line and ":" in line:
                    ip = line.split(':')[-1].strip()
                    if ip and ip.startswith('192.168.'):
                        return ip
    except:
        pass
    return None

def test_access(ip, port, service_name, path=""):
    """测试访问"""
    url = f"http://{ip}:{port}{path}"
    try:
        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            print(f"✅ {service_name}: {url}")
            return True
        else:
            print(f"⚠️ {service_name}: {url} (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ {service_name}: {url} (错误: {e})")
        return False

def main():
    print("🚀 快速局域网访问测试")
    print("=" * 40)
    
    # 获取IP地址
    main_ip = get_main_ip()
    wifi_ip = get_wifi_ip()
    
    print("📍 检测到的IP地址:")
    if main_ip:
        print(f"   主要IP: {main_ip}")
    if wifi_ip:
        print(f"   WiFi IP: {wifi_ip}")
    
    print("\n🔍 测试访问:")
    
    # 测试本地访问
    print("\n本地访问:")
    test_access("localhost", 8084, "前端服务")
    test_access("localhost", 8086, "API服务", "/api/health")

    # 测试局域网访问
    if main_ip:
        print(f"\n主要IP ({main_ip}) 访问:")
        frontend_ok = test_access(main_ip, 8084, "前端服务")
        api_ok = test_access(main_ip, 8086, "API服务", "/api/health")
        
        if frontend_ok and api_ok:
            print(f"\n🎉 局域网访问成功！")
            print(f"📱 其他设备可以访问: http://{main_ip}:8084")
        else:
            print(f"\n⚠️ 局域网访问有问题，请检查防火墙设置")
    
    if wifi_ip and wifi_ip != main_ip:
        print(f"\nWiFi IP ({wifi_ip}) 访问:")
        frontend_ok = test_access(wifi_ip, 8084, "前端服务")
        api_ok = test_access(wifi_ip, 8086, "API服务", "/api/health")
        
        if frontend_ok and api_ok:
            print(f"\n🎉 WiFi局域网访问成功！")
            print(f"📱 WiFi设备可以访问: http://{wifi_ip}:8084")
        else:
            print(f"\n⚠️ WiFi局域网访问有问题，请检查防火墙设置")
    
    print("\n💡 如果局域网访问失败，请:")
    print("1. 右键 '配置防火墙.bat' -> '以管理员身份运行'")
    print("2. 检查杀毒软件是否阻止了网络访问")
    print("3. 确保其他设备连接到同一网络")

if __name__ == "__main__":
    main()
