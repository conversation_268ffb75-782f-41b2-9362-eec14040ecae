#!/usr/bin/env python3
"""
检查数据库中的用户
"""

from config.database import DatabaseManager
from models.user import User

def check_users():
    """检查数据库中的用户"""
    try:
        db_manager = DatabaseManager()
        db_manager.initialize()
        
        with db_manager.get_session() as session:
            users = session.query(User).all()
            
            print(f"数据库中共有 {len(users)} 个用户:")
            print("-" * 60)
            
            for user in users:
                print(f"ID: {user.id}")
                print(f"用户名: {user.username}")
                print(f"邮箱: {user.email}")
                print(f"是否激活: {user.is_active}")
                print(f"是否管理员: {user.is_admin}")
                print(f"是否被禁用: {user.is_banned}")
                print(f"登录次数: {user.login_count}")
                print(f"最后登录: {user.last_login}")
                print(f"创建时间: {user.created_at}")
                print("-" * 60)
                
    except Exception as e:
        print(f"检查用户失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_users()
